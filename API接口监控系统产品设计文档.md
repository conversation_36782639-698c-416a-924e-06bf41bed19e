# API接口监控系统产品设计文档

## 1. 项目概述

### 1.1 项目背景
基于Spring Boot框架开发的API接口监控系统，用于实时监控API接口的可用性、性能指标和异常情况。

### 1.2 核心功能
- 实时监控API接口状态
- 记录接口调用统计数据
- 异常检测和日志记录
- 可视化监控面板
- 接口详情查看

## 2. 功能需求

### 2.1 监控指标
- **接口名称**: 接口的业务名称
- **接口路径**: API的URL路径
- **状态**: 正常/异常/超时
- **今日调用量**: 当日累计调用次数
- **平均响应时间**: 接口响应时间统计
- **错误率**: 异常请求占比
- **异常报错信息**: 详细的错误信息

### 2.2 监控范围
- HTTP状态码错误（500、404等）
- 业务逻辑错误（如code: -10014）
- 响应超时
- 网络连接异常

### 2.3 页面功能
- **监控列表页**: 展示所有接口的监控概览
- **接口详情页**: 查看单个接口的详细监控数据
- **实时状态更新**: 定时刷新监控数据

## 3. 技术架构

### 3.1 技术栈
- **后端框架**: Spring Boot 2.7+
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **前端**: Thymeleaf + Bootstrap + jQuery
- **监控**: 自定义拦截器 + 定时任务
- **日志**: Logback

### 3.2 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端页面      │    │   Spring Boot   │    │     MySQL       │
│   (监控面板)    │◄──►│   (核心服务)    │◄──►│   (数据存储)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │     Redis       │
                       │   (缓存层)      │
                       └─────────────────┘
```

## 4. 数据库设计

### 4.1 接口配置表 (api_config)
```sql
CREATE TABLE api_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    api_name VARCHAR(100) NOT NULL COMMENT '接口名称',
    api_path VARCHAR(200) NOT NULL COMMENT '接口路径',
    api_method VARCHAR(10) DEFAULT 'GET' COMMENT '请求方法',
    timeout_ms INT DEFAULT 5000 COMMENT '超时时间(毫秒)',
    check_interval INT DEFAULT 60 COMMENT '检查间隔(秒)',
    is_enabled TINYINT DEFAULT 1 COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4.2 监控记录表 (api_monitor_record)
```sql
CREATE TABLE api_monitor_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    api_id BIGINT NOT NULL COMMENT '接口ID',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    response_time INT COMMENT '响应时间(毫秒)',
    http_status INT COMMENT 'HTTP状态码',
    error_code VARCHAR(50) COMMENT '业务错误码',
    error_message TEXT COMMENT '错误信息',
    check_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
    INDEX idx_api_id_time (api_id, check_time),
    INDEX idx_check_time (check_time)
);
```

### 4.3 统计汇总表 (api_statistics)
```sql
CREATE TABLE api_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    api_id BIGINT NOT NULL,
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_count INT DEFAULT 0 COMMENT '总调用次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    avg_response_time DECIMAL(10,2) COMMENT '平均响应时间',
    max_response_time INT COMMENT '最大响应时间',
    min_response_time INT COMMENT '最小响应时间',
    error_rate DECIMAL(5,2) COMMENT '错误率',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_api_date (api_id, stat_date)
);
```

## 5. 核心功能模块

### 5.1 监控拦截器
- 拦截所有API请求
- 记录请求开始时间
- 捕获响应结果和异常
- 计算响应时间
- 异步记录监控数据

### 5.2 定时监控任务
- 定期检查配置的API接口
- 模拟请求验证接口可用性
- 更新接口状态
- 生成统计报告

### 5.3 数据统计服务
- 实时计算监控指标
- 生成日、周、月统计报告
- 缓存热点数据
- 清理历史数据

### 5.4 异常检测
- HTTP状态码检测
- 业务错误码检测
- 响应时间阈值检测
- 连续失败检测

## 6. 页面设计

### 6.1 监控列表页
- 表格展示所有接口监控状态
- 支持按状态筛选
- 支持搜索功能
- 实时刷新数据
- 点击进入详情页

### 6.2 接口详情页
- 接口基本信息
- 实时状态图表
- 响应时间趋势
- 错误日志列表
- 历史统计数据

## 7. 日志管理

### 7.1 日志分类
- **访问日志**: 记录所有API访问
- **错误日志**: 记录异常和错误
- **监控日志**: 记录监控任务执行
- **系统日志**: 记录系统运行状态

### 7.2 日志格式
```
[时间] [级别] [线程] [类名] - 接口路径 | 响应时间 | 状态码 | 错误信息
```

## 8. 性能优化

### 8.1 缓存策略
- Redis缓存热点监控数据
- 本地缓存接口配置信息
- 异步处理监控记录

### 8.2 数据库优化
- 合理设计索引
- 分表存储历史数据
- 定期清理过期数据

## 9. 部署方案

### 9.1 环境要求
- JDK 8+
- MySQL 8.0+
- Redis 6.0+

### 9.2 配置文件
- application.yml: 主配置文件
- logback-spring.xml: 日志配置
- 监控接口配置

## 10. 扩展功能

### 10.1 告警通知
- 邮件告警
- 短信通知
- 钉钉/企业微信通知

### 10.2 数据导出
- Excel报表导出
- 监控数据API
- 图表截图功能

## 11. 安全考虑

### 11.1 访问控制
- 登录认证
- 权限管理
- IP白名单

### 11.2 数据安全
- 敏感信息脱敏
- 数据加密存储
- 审计日志
