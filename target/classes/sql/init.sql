-- API监控系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS api_monitor DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE api_monitor;

-- 1. API配置表
CREATE TABLE IF NOT EXISTS api_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    api_name VARCHAR(100) NOT NULL COMMENT '接口名称',
    api_path VARCHAR(200) NOT NULL COMMENT '接口路径',
    api_method VARCHAR(10) DEFAULT 'GET' COMMENT '请求方法',
    timeout_ms INT DEFAULT 5000 COMMENT '超时时间(毫秒)',
    check_interval INT DEFAULT 60 COMMENT '检查间隔(秒)',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    headers TEXT COMMENT '请求头信息(JSON格式)',
    request_body TEXT COMMENT '请求体(JSON格式)',
    expected_response TEXT COMMENT '期望的响应内容',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_api_path (api_path),
    INDEX idx_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API配置表';

-- 2. API监控记录表
CREATE TABLE IF NOT EXISTS api_monitor_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    api_id BIGINT NOT NULL COMMENT '接口ID',
    status VARCHAR(20) NOT NULL COMMENT '状态: SUCCESS, ERROR, TIMEOUT, NETWORK_ERROR',
    response_time INT COMMENT '响应时间(毫秒)',
    http_status INT COMMENT 'HTTP状态码',
    error_code VARCHAR(50) COMMENT '业务错误码',
    error_message TEXT COMMENT '错误信息',
    response_content VARCHAR(1000) COMMENT '响应内容(截取前1000字符)',
    check_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
    check_type VARCHAR(20) DEFAULT 'SCHEDULED' COMMENT '检查类型: SCHEDULED, MANUAL, INTERCEPTOR',
    INDEX idx_api_id_time (api_id, check_time),
    INDEX idx_check_time (check_time),
    INDEX idx_status (status),
    FOREIGN KEY (api_id) REFERENCES api_config(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API监控记录表';

-- 3. API统计数据表
CREATE TABLE IF NOT EXISTS api_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    api_id BIGINT NOT NULL COMMENT '接口ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_count INT DEFAULT 0 COMMENT '总调用次数',
    success_count INT DEFAULT 0 COMMENT '成功次数',
    error_count INT DEFAULT 0 COMMENT '错误次数',
    avg_response_time DECIMAL(10,2) COMMENT '平均响应时间',
    max_response_time INT COMMENT '最大响应时间',
    min_response_time INT COMMENT '最小响应时间',
    error_rate DECIMAL(5,2) COMMENT '错误率',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_api_date (api_id, stat_date),
    INDEX idx_stat_date (stat_date),
    FOREIGN KEY (api_id) REFERENCES api_config(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API统计数据表';

-- 插入示例数据
INSERT INTO api_config (api_name, api_path, api_method, timeout_ms, check_interval, is_enabled) VALUES
('用户登录接口', 'http://localhost:8080/api/user/login', 'POST', 5000, 60, 1),
('用户信息查询', 'http://localhost:8080/api/user/info', 'GET', 3000, 30, 1),
('商品列表接口', 'http://localhost:8080/api/product/list', 'GET', 5000, 60, 1),
('订单创建接口', 'http://localhost:8080/api/order/create', 'POST', 10000, 120, 1),
('支付接口', 'http://localhost:8080/api/payment/pay', 'POST', 15000, 300, 1),
('百度首页', 'https://www.baidu.com', 'GET', 5000, 60, 1),
('GitHub API', 'https://api.github.com', 'GET', 5000, 120, 1);

-- 插入一些示例监控记录
INSERT INTO api_monitor_record (api_id, status, response_time, http_status, check_time, check_type) VALUES
(1, 'SUCCESS', 120, 200, DATE_SUB(NOW(), INTERVAL 1 MINUTE), 'SCHEDULED'),
(1, 'SUCCESS', 135, 200, DATE_SUB(NOW(), INTERVAL 2 MINUTE), 'SCHEDULED'),
(1, 'ERROR', 5000, 500, DATE_SUB(NOW(), INTERVAL 3 MINUTE), 'SCHEDULED'),
(2, 'SUCCESS', 89, 200, DATE_SUB(NOW(), INTERVAL 1 MINUTE), 'SCHEDULED'),
(2, 'SUCCESS', 95, 200, DATE_SUB(NOW(), INTERVAL 2 MINUTE), 'SCHEDULED'),
(3, 'SUCCESS', 200, 200, DATE_SUB(NOW(), INTERVAL 1 MINUTE), 'SCHEDULED'),
(6, 'SUCCESS', 150, 200, DATE_SUB(NOW(), INTERVAL 1 MINUTE), 'SCHEDULED'),
(6, 'SUCCESS', 145, 200, DATE_SUB(NOW(), INTERVAL 2 MINUTE), 'SCHEDULED'),
(7, 'SUCCESS', 300, 200, DATE_SUB(NOW(), INTERVAL 1 MINUTE), 'SCHEDULED');

-- 插入今日统计数据
INSERT INTO api_statistics (api_id, stat_date, total_count, success_count, error_count, avg_response_time, max_response_time, min_response_time, error_rate) VALUES
(1, CURDATE(), 100, 95, 5, 125.50, 200, 80, 5.00),
(2, CURDATE(), 150, 148, 2, 92.30, 120, 70, 1.33),
(3, CURDATE(), 80, 78, 2, 180.20, 250, 120, 2.50),
(4, CURDATE(), 50, 48, 2, 2500.00, 3000, 2000, 4.00),
(5, CURDATE(), 30, 29, 1, 8500.00, 10000, 7000, 3.33),
(6, CURDATE(), 200, 200, 0, 145.00, 200, 100, 0.00),
(7, CURDATE(), 120, 118, 2, 280.50, 400, 200, 1.67);

-- 创建索引优化查询性能
CREATE INDEX idx_api_monitor_record_api_status ON api_monitor_record(api_id, status);
CREATE INDEX idx_api_monitor_record_check_time_desc ON api_monitor_record(check_time DESC);
CREATE INDEX idx_api_statistics_api_date_desc ON api_statistics(api_id, stat_date DESC);

-- 创建视图：API监控概览
CREATE OR REPLACE VIEW v_api_monitor_overview AS
SELECT 
    ac.id as api_id,
    ac.api_name,
    ac.api_path,
    ac.api_method,
    ac.is_enabled,
    ac.timeout_ms,
    ac.check_interval,
    -- 最新状态
    (SELECT status FROM api_monitor_record WHERE api_id = ac.id ORDER BY check_time DESC LIMIT 1) as latest_status,
    (SELECT check_time FROM api_monitor_record WHERE api_id = ac.id ORDER BY check_time DESC LIMIT 1) as last_check_time,
    (SELECT error_message FROM api_monitor_record WHERE api_id = ac.id AND error_message IS NOT NULL ORDER BY check_time DESC LIMIT 1) as last_error_message,
    -- 今日统计
    COALESCE(ast.total_count, 0) as today_total_count,
    COALESCE(ast.success_count, 0) as today_success_count,
    COALESCE(ast.error_count, 0) as today_error_count,
    COALESCE(ast.avg_response_time, 0) as today_avg_response_time,
    COALESCE(ast.error_rate, 0) as today_error_rate
FROM api_config ac
LEFT JOIN api_statistics ast ON ac.id = ast.api_id AND ast.stat_date = CURDATE()
ORDER BY ac.id;

-- 创建存储过程：清理历史数据
DELIMITER //
CREATE PROCEDURE CleanupHistoryData(
    IN keep_record_days INT DEFAULT 30,
    IN keep_statistics_days INT DEFAULT 90
)
BEGIN
    DECLARE record_count INT DEFAULT 0;
    DECLARE statistics_count INT DEFAULT 0;
    
    -- 清理监控记录
    DELETE FROM api_monitor_record 
    WHERE check_time < DATE_SUB(NOW(), INTERVAL keep_record_days DAY);
    
    SET record_count = ROW_COUNT();
    
    -- 清理统计数据
    DELETE FROM api_statistics 
    WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL keep_statistics_days DAY);
    
    SET statistics_count = ROW_COUNT();
    
    -- 输出清理结果
    SELECT 
        record_count as cleaned_records,
        statistics_count as cleaned_statistics,
        NOW() as cleanup_time;
END //
DELIMITER ;

-- 创建存储过程：更新统计数据
DELIMITER //
CREATE PROCEDURE UpdateDailyStatistics(IN target_date DATE)
BEGIN
    -- 如果没有指定日期，使用当前日期
    IF target_date IS NULL THEN
        SET target_date = CURDATE();
    END IF;
    
    -- 更新或插入统计数据
    INSERT INTO api_statistics (
        api_id, stat_date, total_count, success_count, error_count,
        avg_response_time, max_response_time, min_response_time, error_rate
    )
    SELECT 
        api_id,
        target_date,
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN status != 'SUCCESS' THEN 1 ELSE 0 END) as error_count,
        AVG(CASE WHEN response_time IS NOT NULL THEN response_time END) as avg_response_time,
        MAX(response_time) as max_response_time,
        MIN(response_time) as min_response_time,
        ROUND(SUM(CASE WHEN status != 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as error_rate
    FROM api_monitor_record
    WHERE DATE(check_time) = target_date
    GROUP BY api_id
    ON DUPLICATE KEY UPDATE
        total_count = VALUES(total_count),
        success_count = VALUES(success_count),
        error_count = VALUES(error_count),
        avg_response_time = VALUES(avg_response_time),
        max_response_time = VALUES(max_response_time),
        min_response_time = VALUES(min_response_time),
        error_rate = VALUES(error_rate),
        updated_time = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- 创建事件调度器：自动更新统计数据
-- 注意：需要确保MySQL的事件调度器已启用 (SET GLOBAL event_scheduler = ON;)
CREATE EVENT IF NOT EXISTS evt_update_daily_statistics
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
  CALL UpdateDailyStatistics(CURDATE());

-- 创建事件调度器：自动清理历史数据
CREATE EVENT IF NOT EXISTS evt_cleanup_history_data
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CleanupHistoryData(30, 90);

-- 显示创建结果
SELECT 'API监控系统数据库初始化完成！' as message;
SELECT COUNT(*) as api_config_count FROM api_config;
SELECT COUNT(*) as monitor_record_count FROM api_monitor_record;
SELECT COUNT(*) as statistics_count FROM api_statistics;
