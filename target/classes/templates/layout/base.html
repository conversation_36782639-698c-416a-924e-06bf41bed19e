<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title != null ? title + ' - API监控系统' : 'API监控系统'}">API监控系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .status-badge {
            font-size: 0.875rem;
        }
        .card-stats {
            border-left: 4px solid;
        }
        .card-stats.success {
            border-left-color: #28a745;
        }
        .card-stats.danger {
            border-left-color: #dc3545;
        }
        .card-stats.warning {
            border-left-color: #ffc107;
        }
        .card-stats.info {
            border-left-color: #17a2b8;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: inline-block;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-success {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
        .status-warning {
            background-color: #ffc107;
        }
        .status-unknown {
            background-color: #6c757d;
        }
        .auto-refresh {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <th:block th:fragment="head-extra">
        <!-- 页面特定的CSS和JS可以在这里添加 -->
    </th:block>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/api-monitor/monitor">
                <i class="fas fa-chart-line me-2"></i>API监控系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:classappend="${#request.requestURI.contains('/monitor/index') or #request.requestURI.endsWith('/monitor') or #request.requestURI.endsWith('/monitor/') ? 'active' : ''}" 
                           href="/api-monitor/monitor">
                            <i class="fas fa-tachometer-alt me-1"></i>监控概览
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:classappend="${#request.requestURI.contains('/monitor/config') ? 'active' : ''}" 
                           href="/api-monitor/monitor/config">
                            <i class="fas fa-cog me-1"></i>配置管理
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tools me-1"></i>工具
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/api-monitor/druid" target="_blank">
                                <i class="fas fa-database me-1"></i>数据库监控
                            </a></li>
                            <li><a class="dropdown-item" href="/api-monitor/actuator/health" target="_blank">
                                <i class="fas fa-heartbeat me-1"></i>健康检查
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-4">
        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}">错误信息</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 成功提示 -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <span th:text="${success}">成功信息</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 页面内容 -->
        <div th:fragment="content">
            <!-- 页面特定内容在这里 -->
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <span class="text-muted">© 2025 API监控系统 - 实时监控您的API服务</span>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 全局配置
        const API_BASE_URL = '/api-monitor/monitor/api';
        
        // 通用工具函数
        const Utils = {
            // 显示加载状态
            showLoading: function(element) {
                if (element) {
                    element.classList.add('loading', 'show');
                }
            },
            
            // 隐藏加载状态
            hideLoading: function(element) {
                if (element) {
                    element.classList.remove('loading', 'show');
                }
            },
            
            // 显示提示消息
            showAlert: function(message, type = 'info') {
                const alertHtml = `
                    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                $('main .container-fluid').prepend(alertHtml);
                
                // 3秒后自动关闭
                setTimeout(() => {
                    $('.alert').alert('close');
                }, 3000);
            },
            
            // 格式化时间
            formatDateTime: function(dateTime) {
                if (!dateTime) return '-';
                const date = new Date(dateTime);
                return date.toLocaleString('zh-CN');
            },
            
            // 格式化响应时间
            formatResponseTime: function(time) {
                if (!time) return '-';
                return time + 'ms';
            }
        };
        
        // 页面加载完成后执行
        $(document).ready(function() {
            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
    
    <th:block th:fragment="scripts">
        <!-- 页面特定的JavaScript可以在这里添加 -->
    </th:block>
</body>
</html>
