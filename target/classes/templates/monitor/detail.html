<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html}">
<head th:replace="~{layout/base :: head}">
    <title>接口详情 - API监控系统</title>
</head>
<body>
    <main th:fragment="content">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/api-monitor/monitor">监控概览</a></li>
                <li class="breadcrumb-item active" aria-current="page">接口详情</li>
            </ol>
        </nav>

        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-info-circle me-2"></i>接口详情</h2>
            <div>
                <button class="btn btn-outline-success" th:onclick="'manualCheck(' + ${detail.apiId} + ')'">
                    <i class="fas fa-sync-alt me-1"></i>手动检查
                </button>
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-refresh me-1"></i>刷新数据
                </button>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：基本信息和当前状态 -->
            <div class="col-md-4">
                <!-- 基本信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info me-2"></i>基本信息</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>接口名称:</strong></td>
                                <td th:text="${detail.apiName}">接口名称</td>
                            </tr>
                            <tr>
                                <td><strong>接口路径:</strong></td>
                                <td><code th:text="${detail.apiPath}">接口路径</code></td>
                            </tr>
                            <tr>
                                <td><strong>请求方法:</strong></td>
                                <td>
                                    <span class="badge bg-info" th:text="${detail.apiMethod}">GET</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>超时时间:</strong></td>
                                <td th:text="${detail.timeoutMs + 'ms'}">5000ms</td>
                            </tr>
                            <tr>
                                <td><strong>检查间隔:</strong></td>
                                <td th:text="${detail.checkInterval + '秒'}">60秒</td>
                            </tr>
                            <tr>
                                <td><strong>启用状态:</strong></td>
                                <td>
                                    <span class="badge" th:classappend="${detail.isEnabled ? 'bg-success' : 'bg-secondary'}"
                                          th:text="${detail.isEnabled ? '已启用' : '已禁用'}">已启用</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 当前状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>当前状态</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i th:class="${detail.statusIcon} + ' fa-3x'" th:classappend="'text-' + ${detail.statusClass}"></i>
                        </div>
                        <h4>
                            <span class="badge" th:classappend="'bg-' + ${detail.statusClass}">
                                <span th:switch="${detail.currentStatus}">
                                    <span th:case="'SUCCESS'">正常</span>
                                    <span th:case="'ERROR'">异常</span>
                                    <span th:case="'TIMEOUT'">超时</span>
                                    <span th:case="'NETWORK_ERROR'">网络错误</span>
                                    <span th:case="*">未知</span>
                                </span>
                            </span>
                        </h4>
                        <div class="mt-3">
                            <small class="text-muted">最后检查时间:</small><br>
                            <span th:if="${detail.lastCheckTime != null}" 
                                  th:text="${#temporals.format(detail.lastCheckTime, 'yyyy-MM-dd HH:mm:ss')}">-</span>
                            <span th:unless="${detail.lastCheckTime != null}">未检查</span>
                        </div>
                        <div th:if="${detail.lastResponseTime != null}" class="mt-2">
                            <small class="text-muted">响应时间:</small><br>
                            <span th:text="${detail.lastResponseTime + 'ms'}">-</span>
                        </div>
                        <div th:if="${detail.lastErrorMessage != null}" class="mt-2">
                            <small class="text-muted">错误信息:</small><br>
                            <span class="text-danger" th:text="${detail.lastErrorMessage}">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：统计数据和监控记录 -->
            <div class="col-md-8">
                <!-- 今日统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">总调用量</h5>
                                <h3 class="mb-0" th:text="${detail.todayTotalCount != null ? detail.todayTotalCount : 0}">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">成功次数</h5>
                                <h3 class="mb-0" th:text="${detail.todaySuccessCount != null ? detail.todaySuccessCount : 0}">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">错误次数</h5>
                                <h3 class="mb-0" th:text="${detail.todayErrorCount != null ? detail.todayErrorCount : 0}">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">错误率</h5>
                                <h3 class="mb-0">
                                    <span th:if="${detail.todayErrorRate != null}" 
                                          th:text="${detail.todayErrorRate + '%'}">0%</span>
                                    <span th:unless="${detail.todayErrorRate != null}">0%</span>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 响应时间趋势图 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>响应时间趋势</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="responseTimeChart" height="100"></canvas>
                    </div>
                </div>

                <!-- 最近监控记录 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>最近监控记录</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>检查时间</th>
                                        <th>状态</th>
                                        <th>响应时间</th>
                                        <th>HTTP状态</th>
                                        <th>错误信息</th>
                                        <th>检查类型</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="record : ${detail.recentRecords}">
                                        <td>
                                            <small th:text="${#temporals.format(record.checkTime, 'MM-dd HH:mm:ss')}">时间</small>
                                        </td>
                                        <td>
                                            <span class="badge" th:classappend="'bg-' + ${record.statusClass}">
                                                <span th:switch="${record.status}">
                                                    <span th:case="'SUCCESS'">正常</span>
                                                    <span th:case="'ERROR'">异常</span>
                                                    <span th:case="'TIMEOUT'">超时</span>
                                                    <span th:case="'NETWORK_ERROR'">网络错误</span>
                                                    <span th:case="*">未知</span>
                                                </span>
                                            </span>
                                        </td>
                                        <td>
                                            <span th:if="${record.responseTime != null}" 
                                                  th:text="${record.responseTime + 'ms'}">-</span>
                                            <span th:unless="${record.responseTime != null}">-</span>
                                        </td>
                                        <td>
                                            <span th:if="${record.httpStatus != null}" 
                                                  th:text="${record.httpStatus}"
                                                  th:classappend="${record.httpStatus >= 400 ? 'text-danger' : 'text-success'}">-</span>
                                            <span th:unless="${record.httpStatus != null}">-</span>
                                        </td>
                                        <td>
                                            <span th:if="${record.errorMessage != null}" 
                                                  th:text="${#strings.abbreviate(record.errorMessage, 50)}"
                                                  th:title="${record.errorMessage}"
                                                  class="text-danger">-</span>
                                            <span th:unless="${record.errorMessage != null}">-</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <span th:switch="${record.checkType}">
                                                    <span th:case="'SCHEDULED'">定时</span>
                                                    <span th:case="'MANUAL'">手动</span>
                                                    <span th:case="'INTERCEPTOR'">拦截</span>
                                                    <span th:case="*" th:text="${record.checkType}">未知</span>
                                                </span>
                                            </small>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <th:block th:fragment="scripts">
        <script>
            let responseTimeChart;
            
            // 页面加载完成后初始化
            $(document).ready(function() {
                initResponseTimeChart();
            });
            
            // 初始化响应时间趋势图
            function initResponseTimeChart() {
                const ctx = document.getElementById('responseTimeChart').getContext('2d');
                
                // 从最近记录中提取数据
                const records = /*[[${detail.recentRecords}]]*/ [];
                const labels = [];
                const data = [];
                
                // 取最近20条记录，按时间正序排列
                records.slice(-20).reverse().forEach(record => {
                    if (record.responseTime) {
                        labels.push(new Date(record.checkTime).toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                        }));
                        data.push(record.responseTime);
                    }
                });
                
                responseTimeChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '响应时间 (ms)',
                            data: data,
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.1)',
                            tension: 0.1,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '响应时间 (ms)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: '时间'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
            
            // 手动检查API
            function manualCheck(apiId) {
                const button = event.target.closest('button');
                const icon = button.querySelector('i');
                
                icon.classList.add('auto-refresh');
                button.disabled = true;
                
                fetch(API_BASE_URL + '/check/' + apiId, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Utils.showAlert(`检查完成: ${data.status}`, 'success');
                        // 延迟刷新页面以确保数据库已更新
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        Utils.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('手动检查失败:', error);
                    Utils.showAlert('检查失败', 'danger');
                })
                .finally(() => {
                    icon.classList.remove('auto-refresh');
                    button.disabled = false;
                });
            }
            
            // 刷新数据
            function refreshData() {
                location.reload();
            }
        </script>
    </th:block>
</body>
</html>
