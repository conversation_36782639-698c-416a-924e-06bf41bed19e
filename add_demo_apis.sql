-- 添加Demo测试接口
USE api_monitor;

-- 清除现有的测试接口（如果存在）
DELETE FROM api_config WHERE api_path LIKE '%/api/test/%';

-- 添加测试接口
INSERT INTO api_config (api_name, api_path, api_method, timeout_ms, check_interval, is_enabled) VALUES
('✅ 测试成功接口', 'http://localhost:8080/api-monitor/api/test/success', 'GET', 5000, 60, 1),
('🎲 测试随机接口', 'http://localhost:8080/api-monitor/api/test/random', 'GET', 5000, 30, 1),
('❌ 测试业务错误接口', 'http://localhost:8080/api-monitor/api/test/business-error', 'GET', 5000, 60, 1),
('🐌 测试慢响应接口', 'http://localhost:8080/api-monitor/api/test/slow', 'GET', 10000, 120, 1),
('💥 测试异常接口', 'http://localhost:8080/api-monitor/api/test/exception', 'GET', 5000, 60, 1),
('📝 测试POST接口', 'http://localhost:8080/api-monitor/api/test/post-test', 'POST', 5000, 60, 1),
('💚 测试健康检查', 'http://localhost:8080/api-monitor/api/test/health', 'GET', 3000, 30, 1),
('👤 测试用户接口', 'http://localhost:8080/api-monitor/api/test/user/123', 'GET', 5000, 60, 1);

-- 查看添加的接口
SELECT id, api_name, api_path, api_method, is_enabled FROM api_config WHERE api_path LIKE '%/api/test/%';
