#!/bin/bash

# Demo API测试脚本
# 用于快速测试所有Demo接口

BASE_URL="http://localhost:8080/api-monitor/api/test"

echo "🚀 开始测试Demo API接口..."
echo "=================================="

# 测试成功接口
echo "✅ 测试成功接口:"
curl -s "$BASE_URL/success" | jq '.'
echo ""

# 测试随机接口
echo "🎲 测试随机接口:"
curl -s "$BASE_URL/random" | jq '.'
echo ""

# 测试业务错误接口
echo "❌ 测试业务错误接口:"
curl -s "$BASE_URL/business-error" | jq '.'
echo ""

# 测试健康检查接口
echo "💚 测试健康检查接口:"
curl -s "$BASE_URL/health" | jq '.'
echo ""

# 测试用户接口
echo "👤 测试用户接口 (存在的用户):"
curl -s "$BASE_URL/user/123" | jq '.'
echo ""

echo "👤 测试用户接口 (不存在的用户):"
curl -s "$BASE_URL/user/9999" | jq '.'
echo ""

# 测试POST接口
echo "📝 测试POST接口:"
curl -s -X POST "$BASE_URL/post-test" \
  -H "Content-Type: application/json" \
  -d '{"name":"测试用户","email":"<EMAIL>"}' | jq '.'
echo ""

# 测试异常接口（可能会抛出异常）
echo "💥 测试异常接口:"
curl -s "$BASE_URL/exception" | jq '.' || echo "接口可能抛出了异常"
echo ""

echo "=================================="
echo "🎉 Demo API测试完成！"
echo ""
echo "📊 现在可以访问监控页面查看结果:"
echo "   http://localhost:8080/api-monitor/monitor"
