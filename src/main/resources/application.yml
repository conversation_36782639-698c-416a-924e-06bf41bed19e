server:
  port: 8080
  servlet:
    context-path: /api-monitor

spring:
  application:
    name: api-monitor-system
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: root
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 10
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # Thymeleaf配置
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

# 日志配置
logging:
  level:
    com.monitor: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/api-monitor.log
    max-size: 100MB
    max-history: 30

# 监控配置
monitor:
  # 默认超时时间(毫秒)
  default-timeout: 5000
  # 默认检查间隔(秒)
  default-check-interval: 60
  # 线程池配置
  thread-pool:
    core-size: 5
    max-size: 10
    queue-capacity: 100
    keep-alive-seconds: 60
  # 缓存配置
  cache:
    # 统计数据缓存时间(秒)
    statistics-ttl: 300
    # 接口配置缓存时间(秒)
    config-ttl: 600
  # 数据清理配置
  cleanup:
    # 保留监控记录天数
    keep-record-days: 30
    # 保留统计数据天数
    keep-statistics-days: 90
    # 清理任务执行时间(cron表达式)
    cron: "0 0 2 * * ?"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
