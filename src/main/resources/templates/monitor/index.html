<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html}">
<head th:replace="~{layout/base :: head}">
    <title>监控概览 - API监控系统</title>
</head>
<body>
    <main th:fragment="content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-tachometer-alt me-2"></i>监控概览</h2>
            <div>
                <button class="btn btn-outline-primary" id="refreshBtn" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>刷新数据
                </button>
                <div class="form-check form-switch d-inline-block ms-3">
                    <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                    <label class="form-check-label" for="autoRefresh">自动刷新</label>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-stats success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-success">正常接口</h5>
                                <h3 class="mb-0" th:text="${successCount}">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-danger">异常接口</h5>
                                <h3 class="mb-0" th:text="${errorCount}">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-times-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-warning">超时接口</h5>
                                <h3 class="mb-0" th:text="${timeoutCount}">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stats info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title text-info">总接口数</h5>
                                <h3 class="mb-0" th:text="${totalCount}">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-list fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索接口名称或路径...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="SUCCESS">正常</option>
                            <option value="ERROR">异常</option>
                            <option value="TIMEOUT">超时</option>
                            <option value="UNKNOWN">未知</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="enabledFilter">
                            <option value="">全部</option>
                            <option value="true">已启用</option>
                            <option value="false">已禁用</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>接口监控列表</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="monitorTable">
                        <thead class="table-light">
                            <tr>
                                <th>状态</th>
                                <th>接口名称</th>
                                <th>接口路径</th>
                                <th>今日调用量</th>
                                <th>平均响应时间</th>
                                <th>错误率</th>
                                <th>最后检查时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="monitor : ${monitorList}" th:data-api-id="${monitor.apiId}">
                                <td>
                                    <span class="badge" th:classappend="'bg-' + ${monitor.statusClass}">
                                        <i th:class="${monitor.statusIcon} + ' me-1'"></i>
                                        <span th:switch="${monitor.status}">
                                            <span th:case="'SUCCESS'">正常</span>
                                            <span th:case="'ERROR'">异常</span>
                                            <span th:case="'TIMEOUT'">超时</span>
                                            <span th:case="'NETWORK_ERROR'">网络错误</span>
                                            <span th:case="*">未知</span>
                                        </span>
                                    </span>
                                    <span th:if="${!monitor.isEnabled}" class="badge bg-secondary ms-1">已禁用</span>
                                </td>
                                <td>
                                    <strong th:text="${monitor.apiName}">接口名称</strong>
                                </td>
                                <td>
                                    <code th:text="${monitor.apiPath}">接口路径</code>
                                </td>
                                <td>
                                    <span th:text="${monitor.todayCount != null ? monitor.todayCount : 0}">0</span>
                                </td>
                                <td>
                                    <span th:if="${monitor.avgResponseTime != null}" 
                                          th:text="${monitor.avgResponseTime + 'ms'}">-</span>
                                    <span th:unless="${monitor.avgResponseTime != null}">-</span>
                                </td>
                                <td>
                                    <span th:if="${monitor.errorRate != null}" 
                                          th:text="${monitor.errorRate + '%'}" 
                                          th:classappend="${monitor.errorRate > 10 ? 'text-danger' : monitor.errorRate > 5 ? 'text-warning' : 'text-success'}">0%</span>
                                    <span th:unless="${monitor.errorRate != null}">-</span>
                                </td>
                                <td>
                                    <small th:if="${monitor.lastCheckTime != null}" 
                                           th:text="${#temporals.format(monitor.lastCheckTime, 'MM-dd HH:mm:ss')}">-</small>
                                    <small th:unless="${monitor.lastCheckTime != null}">未检查</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a th:href="@{/monitor/detail/{id}(id=${monitor.apiId})}" 
                                           class="btn btn-outline-primary btn-action" 
                                           data-bs-toggle="tooltip" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-outline-success btn-action" 
                                                th:onclick="'manualCheck(' + ${monitor.apiId} + ')'"
                                                data-bs-toggle="tooltip" title="手动检查">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <th:block th:fragment="scripts">
        <script>
            let autoRefreshInterval;
            
            // 页面加载完成后初始化
            $(document).ready(function() {
                initAutoRefresh();
                initSearch();
            });
            
            // 初始化自动刷新
            function initAutoRefresh() {
                const autoRefreshCheckbox = document.getElementById('autoRefresh');
                if (autoRefreshCheckbox.checked) {
                    startAutoRefresh();
                }
                
                autoRefreshCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        startAutoRefresh();
                    } else {
                        stopAutoRefresh();
                    }
                });
            }
            
            // 开始自动刷新
            function startAutoRefresh() {
                autoRefreshInterval = setInterval(refreshData, 30000); // 30秒刷新一次
            }
            
            // 停止自动刷新
            function stopAutoRefresh() {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
            }
            
            // 刷新数据
            function refreshData() {
                const refreshBtn = document.getElementById('refreshBtn');
                const icon = refreshBtn.querySelector('i');
                
                icon.classList.add('auto-refresh');
                
                fetch(API_BASE_URL + '/overview')
                    .then(response => response.json())
                    .then(data => {
                        updateTable(data);
                        updateStatistics(data);
                        Utils.showAlert('数据刷新成功', 'success');
                    })
                    .catch(error => {
                        console.error('刷新数据失败:', error);
                        Utils.showAlert('数据刷新失败', 'danger');
                    })
                    .finally(() => {
                        icon.classList.remove('auto-refresh');
                    });
            }
            
            // 更新表格数据
            function updateTable(data) {
                const tbody = document.querySelector('#monitorTable tbody');
                tbody.innerHTML = '';
                
                data.forEach(monitor => {
                    const row = createTableRow(monitor);
                    tbody.appendChild(row);
                });
            }
            
            // 创建表格行
            function createTableRow(monitor) {
                const row = document.createElement('tr');
                row.setAttribute('data-api-id', monitor.apiId);
                
                const statusText = {
                    'SUCCESS': '正常',
                    'ERROR': '异常',
                    'TIMEOUT': '超时',
                    'NETWORK_ERROR': '网络错误'
                };
                
                row.innerHTML = `
                    <td>
                        <span class="badge bg-${monitor.statusClass}">
                            <i class="${monitor.statusIcon} me-1"></i>
                            ${statusText[monitor.status] || '未知'}
                        </span>
                        ${!monitor.isEnabled ? '<span class="badge bg-secondary ms-1">已禁用</span>' : ''}
                    </td>
                    <td><strong>${monitor.apiName}</strong></td>
                    <td><code>${monitor.apiPath}</code></td>
                    <td>${monitor.todayCount || 0}</td>
                    <td>${monitor.avgResponseTime ? monitor.avgResponseTime + 'ms' : '-'}</td>
                    <td>
                        <span class="${monitor.errorRate > 10 ? 'text-danger' : monitor.errorRate > 5 ? 'text-warning' : 'text-success'}">
                            ${monitor.errorRate ? monitor.errorRate + '%' : '-'}
                        </span>
                    </td>
                    <td>
                        <small>${monitor.lastCheckTime ? Utils.formatDateTime(monitor.lastCheckTime) : '未检查'}</small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="/api-monitor/monitor/detail/${monitor.apiId}" 
                               class="btn btn-outline-primary btn-action" 
                               data-bs-toggle="tooltip" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="btn btn-outline-success btn-action" 
                                    onclick="manualCheck(${monitor.apiId})"
                                    data-bs-toggle="tooltip" title="手动检查">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </td>
                `;
                
                return row;
            }
            
            // 更新统计数据
            function updateStatistics(data) {
                const successCount = data.filter(m => m.status === 'SUCCESS').length;
                const errorCount = data.filter(m => m.status === 'ERROR').length;
                const timeoutCount = data.filter(m => m.status === 'TIMEOUT').length;
                const totalCount = data.length;
                
                document.querySelector('.card-stats.success h3').textContent = successCount;
                document.querySelector('.card-stats.danger h3').textContent = errorCount;
                document.querySelector('.card-stats.warning h3').textContent = timeoutCount;
                document.querySelector('.card-stats.info h3').textContent = totalCount;
            }
            
            // 手动检查API
            function manualCheck(apiId) {
                const button = event.target.closest('button');
                const icon = button.querySelector('i');
                
                icon.classList.add('auto-refresh');
                button.disabled = true;
                
                fetch(API_BASE_URL + '/check/' + apiId, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Utils.showAlert(`检查完成: ${data.status}`, 'success');
                        // 延迟刷新数据以确保数据库已更新
                        setTimeout(refreshData, 1000);
                    } else {
                        Utils.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('手动检查失败:', error);
                    Utils.showAlert('检查失败', 'danger');
                })
                .finally(() => {
                    icon.classList.remove('auto-refresh');
                    button.disabled = false;
                });
            }
            
            // 初始化搜索功能
            function initSearch() {
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', debounce(applyFilters, 300));
            }
            
            // 应用筛选
            function applyFilters() {
                const searchText = document.getElementById('searchInput').value.toLowerCase();
                const statusFilter = document.getElementById('statusFilter').value;
                const enabledFilter = document.getElementById('enabledFilter').value;
                
                const rows = document.querySelectorAll('#monitorTable tbody tr');
                
                rows.forEach(row => {
                    const apiName = row.cells[1].textContent.toLowerCase();
                    const apiPath = row.cells[2].textContent.toLowerCase();
                    const status = row.querySelector('.badge').textContent.trim();
                    const isEnabled = !row.querySelector('.badge.bg-secondary');
                    
                    let show = true;
                    
                    // 搜索筛选
                    if (searchText && !apiName.includes(searchText) && !apiPath.includes(searchText)) {
                        show = false;
                    }
                    
                    // 状态筛选
                    if (statusFilter && !status.includes(statusFilter)) {
                        show = false;
                    }
                    
                    // 启用状态筛选
                    if (enabledFilter) {
                        const filterEnabled = enabledFilter === 'true';
                        if (isEnabled !== filterEnabled) {
                            show = false;
                        }
                    }
                    
                    row.style.display = show ? '' : 'none';
                });
            }
            
            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        </script>
    </th:block>
</body>
</html>
