<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html}">
<head th:replace="~{layout/base :: head}">
    <title>配置管理 - API监控系统</title>
</head>
<body>
    <main th:fragment="content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-cog me-2"></i>配置管理</h2>
            <button class="btn btn-primary" onclick="showAddModal()">
                <i class="fas fa-plus me-1"></i>添加接口
            </button>
        </div>

        <!-- 配置列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>API配置列表</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>接口名称</th>
                                <th>接口路径</th>
                                <th>请求方法</th>
                                <th>超时时间</th>
                                <th>检查间隔</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="config : ${configs}">
                                <td th:text="${config.id}">1</td>
                                <td>
                                    <strong th:text="${config.apiName}">接口名称</strong>
                                </td>
                                <td>
                                    <code th:text="${config.apiPath}">接口路径</code>
                                </td>
                                <td>
                                    <span class="badge bg-info" th:text="${config.apiMethod}">GET</span>
                                </td>
                                <td th:text="${config.timeoutMs + 'ms'}">5000ms</td>
                                <td th:text="${config.checkInterval + 's'}">60s</td>
                                <td>
                                    <span class="badge" th:classappend="${config.isEnabled ? 'bg-success' : 'bg-secondary'}"
                                          th:text="${config.isEnabled ? '已启用' : '已禁用'}">已启用</span>
                                </td>
                                <td>
                                    <small th:text="${#temporals.format(config.createdTime, 'yyyy-MM-dd HH:mm')}">创建时间</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" 
                                                th:onclick="'editConfig(' + ${config.id} + ')'"
                                                data-bs-toggle="tooltip" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn" 
                                                th:classappend="${config.isEnabled ? 'btn-outline-warning' : 'btn-outline-success'}"
                                                th:onclick="'toggleEnabled(' + ${config.id} + ', ' + ${!config.isEnabled} + ')'"
                                                th:data-bs-title="${config.isEnabled ? '禁用' : '启用'}"
                                                data-bs-toggle="tooltip">
                                            <i th:class="${config.isEnabled ? 'fas fa-pause' : 'fas fa-play'}"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                th:onclick="'deleteConfig(' + ${config.id} + ')'"
                                                data-bs-toggle="tooltip" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 添加/编辑配置模态框 -->
        <div class="modal fade" id="configModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="configModalTitle">添加API配置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="configForm">
                            <input type="hidden" id="configId" name="id">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="apiName" class="form-label">接口名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="apiName" name="apiName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="apiMethod" class="form-label">请求方法</label>
                                        <select class="form-select" id="apiMethod" name="apiMethod">
                                            <option value="GET">GET</option>
                                            <option value="POST">POST</option>
                                            <option value="PUT">PUT</option>
                                            <option value="DELETE">DELETE</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="apiPath" class="form-label">接口路径 <span class="text-danger">*</span></label>
                                <input type="url" class="form-control" id="apiPath" name="apiPath" 
                                       placeholder="http://example.com/api/test" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="timeoutMs" class="form-label">超时时间(毫秒)</label>
                                        <input type="number" class="form-control" id="timeoutMs" name="timeoutMs" 
                                               value="5000" min="1000" max="60000">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="checkInterval" class="form-label">检查间隔(秒)</label>
                                        <input type="number" class="form-control" id="checkInterval" name="checkInterval" 
                                               value="60" min="10" max="3600">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="headers" class="form-label">请求头(JSON格式)</label>
                                <textarea class="form-control" id="headers" name="headers" rows="3" 
                                          placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'></textarea>
                                <div class="form-text">可选，JSON格式的请求头信息</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="requestBody" class="form-label">请求体(JSON格式)</label>
                                <textarea class="form-control" id="requestBody" name="requestBody" rows="3" 
                                          placeholder='{"key": "value"}'></textarea>
                                <div class="form-text">仅POST/PUT请求需要</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isEnabled" name="isEnabled" checked>
                                    <label class="form-check-label" for="isEnabled">
                                        启用监控
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveConfig()">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速添加示例接口 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-magic me-2"></i>快速添加示例接口</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">点击下方按钮可以快速添加一些示例接口用于测试监控功能：</p>
                <div class="d-flex flex-wrap gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="addExampleApi('success')">
                        <i class="fas fa-check me-1"></i>成功接口
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="addExampleApi('random')">
                        <i class="fas fa-dice me-1"></i>随机接口
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="addExampleApi('error')">
                        <i class="fas fa-times me-1"></i>错误接口
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="addExampleApi('slow')">
                        <i class="fas fa-clock me-1"></i>慢接口
                    </button>
                </div>
            </div>
        </div>
    </main>

    <th:block th:fragment="scripts">
        <script>
            let currentConfigId = null;
            
            // 显示添加模态框
            function showAddModal() {
                currentConfigId = null;
                document.getElementById('configModalTitle').textContent = '添加API配置';
                document.getElementById('configForm').reset();
                document.getElementById('configId').value = '';
                document.getElementById('isEnabled').checked = true;
                
                const modal = new bootstrap.Modal(document.getElementById('configModal'));
                modal.show();
            }
            
            // 编辑配置
            function editConfig(configId) {
                currentConfigId = configId;
                document.getElementById('configModalTitle').textContent = '编辑API配置';
                
                // 这里应该从服务器获取配置数据，暂时使用表格中的数据
                const row = document.querySelector(`tr td:first-child:contains('${configId}')`);
                if (row) {
                    const cells = row.parentElement.cells;
                    document.getElementById('configId').value = configId;
                    document.getElementById('apiName').value = cells[1].textContent.trim();
                    document.getElementById('apiPath').value = cells[2].textContent.trim();
                    document.getElementById('apiMethod').value = cells[3].textContent.trim();
                    document.getElementById('timeoutMs').value = parseInt(cells[4].textContent);
                    document.getElementById('checkInterval').value = parseInt(cells[5].textContent);
                    document.getElementById('isEnabled').checked = cells[6].textContent.includes('已启用');
                }
                
                const modal = new bootstrap.Modal(document.getElementById('configModal'));
                modal.show();
            }
            
            // 保存配置
            function saveConfig() {
                const form = document.getElementById('configForm');
                const formData = new FormData(form);
                
                // 验证JSON格式
                const headers = formData.get('headers');
                const requestBody = formData.get('requestBody');
                
                if (headers && !isValidJSON(headers)) {
                    Utils.showAlert('请求头格式不正确，请输入有效的JSON', 'danger');
                    return;
                }
                
                if (requestBody && !isValidJSON(requestBody)) {
                    Utils.showAlert('请求体格式不正确，请输入有效的JSON', 'danger');
                    return;
                }
                
                // 构建请求数据
                const configData = {
                    id: formData.get('id') || null,
                    apiName: formData.get('apiName'),
                    apiPath: formData.get('apiPath'),
                    apiMethod: formData.get('apiMethod'),
                    timeoutMs: parseInt(formData.get('timeoutMs')),
                    checkInterval: parseInt(formData.get('checkInterval')),
                    headers: headers || null,
                    requestBody: requestBody || null,
                    isEnabled: formData.has('isEnabled')
                };
                
                fetch(API_BASE_URL + '/config/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Utils.showAlert('保存成功', 'success');
                        bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        Utils.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('保存配置失败:', error);
                    Utils.showAlert('保存失败', 'danger');
                });
            }
            
            // 切换启用状态
            function toggleEnabled(configId, enabled) {
                fetch(API_BASE_URL + `/config/${configId}/toggle?enabled=${enabled}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Utils.showAlert(data.message, 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        Utils.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('切换状态失败:', error);
                    Utils.showAlert('操作失败', 'danger');
                });
            }
            
            // 删除配置
            function deleteConfig(configId) {
                if (!confirm('确定要删除这个API配置吗？删除后相关的监控记录也会被清除。')) {
                    return;
                }
                
                fetch(API_BASE_URL + `/config/${configId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Utils.showAlert('删除成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        Utils.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('删除配置失败:', error);
                    Utils.showAlert('删除失败', 'danger');
                });
            }
            
            // 添加示例接口
            function addExampleApi(type) {
                const examples = {
                    success: {
                        apiName: '成功测试接口',
                        apiPath: window.location.origin + '/api-monitor/api/test/success',
                        apiMethod: 'GET',
                        timeoutMs: 5000,
                        checkInterval: 60
                    },
                    random: {
                        apiName: '随机结果接口',
                        apiPath: window.location.origin + '/api-monitor/api/test/random',
                        apiMethod: 'GET',
                        timeoutMs: 5000,
                        checkInterval: 30
                    },
                    error: {
                        apiName: '业务错误接口',
                        apiPath: window.location.origin + '/api-monitor/api/test/business-error',
                        apiMethod: 'GET',
                        timeoutMs: 5000,
                        checkInterval: 60
                    },
                    slow: {
                        apiName: '慢响应接口',
                        apiPath: window.location.origin + '/api-monitor/api/test/slow',
                        apiMethod: 'GET',
                        timeoutMs: 10000,
                        checkInterval: 120
                    }
                };
                
                const config = examples[type];
                if (!config) return;
                
                config.isEnabled = true;
                
                fetch(API_BASE_URL + '/config/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Utils.showAlert('示例接口添加成功', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        Utils.showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('添加示例接口失败:', error);
                    Utils.showAlert('添加失败', 'danger');
                });
            }
            
            // 验证JSON格式
            function isValidJSON(str) {
                if (!str || str.trim() === '') return true;
                try {
                    JSON.parse(str);
                    return true;
                } catch (e) {
                    return false;
                }
            }
        </script>
    </th:block>
</body>
</html>
