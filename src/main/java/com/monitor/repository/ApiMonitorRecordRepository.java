package com.monitor.repository;

import com.monitor.entity.ApiMonitorRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API监控记录数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Repository
public interface ApiMonitorRecordRepository extends JpaRepository<ApiMonitorRecord, Long> {

    /**
     * 根据API ID查询监控记录
     */
    Page<ApiMonitorRecord> findByApiIdOrderByCheckTimeDesc(Long apiId, Pageable pageable);

    /**
     * 查询指定时间范围内的监控记录
     */
    @Query("SELECT r FROM ApiMonitorRecord r WHERE r.apiId = :apiId " +
           "AND r.checkTime BETWEEN :startTime AND :endTime " +
           "ORDER BY r.checkTime DESC")
    List<ApiMonitorRecord> findByApiIdAndTimeRange(@Param("apiId") Long apiId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最新的监控记录
     */
    @Query("SELECT r FROM ApiMonitorRecord r WHERE r.apiId = :apiId " +
           "ORDER BY r.checkTime DESC")
    List<ApiMonitorRecord> findLatestByApiId(@Param("apiId") Long apiId, Pageable pageable);

    /**
     * 统计今日调用量
     */
    @Query("SELECT COUNT(r) FROM ApiMonitorRecord r WHERE r.apiId = :apiId " +
           "AND DATE(r.checkTime) = CURRENT_DATE")
    long countTodayRecords(@Param("apiId") Long apiId);

    /**
     * 统计今日成功调用量
     */
    @Query("SELECT COUNT(r) FROM ApiMonitorRecord r WHERE r.apiId = :apiId " +
           "AND r.status = 'SUCCESS' AND DATE(r.checkTime) = CURRENT_DATE")
    long countTodaySuccessRecords(@Param("apiId") Long apiId);

    /**
     * 计算平均响应时间
     */
    @Query("SELECT AVG(r.responseTime) FROM ApiMonitorRecord r WHERE r.apiId = :apiId " +
           "AND DATE(r.checkTime) = CURRENT_DATE AND r.responseTime IS NOT NULL")
    Double calculateAvgResponseTime(@Param("apiId") Long apiId);

    /**
     * 删除指定时间之前的记录
     */
    @Modifying
    @Query("DELETE FROM ApiMonitorRecord r WHERE r.checkTime < :beforeTime")
    int deleteRecordsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询错误记录
     */
    @Query("SELECT r FROM ApiMonitorRecord r WHERE r.apiId = :apiId " +
           "AND r.status != 'SUCCESS' ORDER BY r.checkTime DESC")
    Page<ApiMonitorRecord> findErrorRecords(@Param("apiId") Long apiId, Pageable pageable);
}
