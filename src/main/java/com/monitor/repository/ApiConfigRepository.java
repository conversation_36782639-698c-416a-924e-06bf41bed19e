package com.monitor.repository;

import com.monitor.entity.ApiConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * API配置数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Repository
public interface ApiConfigRepository extends JpaRepository<ApiConfig, Long> {

    /**
     * 查询所有启用的API配置
     */
    List<ApiConfig> findByIsEnabledTrue();

    /**
     * 根据API路径查询配置
     */
    Optional<ApiConfig> findByApiPath(String apiPath);

    /**
     * 根据API名称查询配置
     */
    Optional<ApiConfig> findByApiName(String apiName);

    /**
     * 查询启用状态的API配置数量
     */
    @Query("SELECT COUNT(a) FROM ApiConfig a WHERE a.isEnabled = true")
    long countEnabledApis();

    /**
     * 根据API路径和方法查询配置
     */
    Optional<ApiConfig> findByApiPathAndApiMethod(String apiPath, String apiMethod);
}
