package com.monitor.repository;

import com.monitor.entity.ApiStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * API统计数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Repository
public interface ApiStatisticsRepository extends JpaRepository<ApiStatistics, Long> {

    /**
     * 根据API ID和日期查询统计数据
     */
    Optional<ApiStatistics> findByApiIdAndStatDate(Long apiId, LocalDate statDate);

    /**
     * 查询指定API的历史统计数据
     */
    @Query("SELECT s FROM ApiStatistics s WHERE s.apiId = :apiId " +
           "AND s.statDate BETWEEN :startDate AND :endDate " +
           "ORDER BY s.statDate DESC")
    List<ApiStatistics> findByApiIdAndDateRange(@Param("apiId") Long apiId,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

    /**
     * 查询今日统计数据
     */
    @Query("SELECT s FROM ApiStatistics s WHERE s.statDate = CURRENT_DATE")
    List<ApiStatistics> findTodayStatistics();

    /**
     * 删除指定日期之前的统计数据
     */
    @Modifying
    @Query("DELETE FROM ApiStatistics s WHERE s.statDate < :beforeDate")
    int deleteStatisticsBefore(@Param("beforeDate") LocalDate beforeDate);

    /**
     * 查询最近N天的统计数据
     */
    @Query("SELECT s FROM ApiStatistics s WHERE s.apiId = :apiId " +
           "AND s.statDate >= :startDate ORDER BY s.statDate DESC")
    List<ApiStatistics> findRecentStatistics(@Param("apiId") Long apiId,
                                            @Param("startDate") LocalDate startDate);
}
