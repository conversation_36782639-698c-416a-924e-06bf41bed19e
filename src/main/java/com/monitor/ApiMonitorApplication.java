package com.monitor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * API监控系统启动类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class ApiMonitorApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiMonitorApplication.class, args);
        System.out.println("=================================");
        System.out.println("API监控系统启动成功！");
        System.out.println("访问地址: http://localhost:8080/api-monitor");
        System.out.println("Druid监控: http://localhost:8080/api-monitor/druid");
        System.out.println("=================================");
    }
}
