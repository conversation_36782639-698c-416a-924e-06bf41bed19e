package com.monitor.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * API监控记录实体类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "api_monitor_record", indexes = {
    @Index(name = "idx_api_id_time", columnList = "api_id,check_time"),
    @Index(name = "idx_check_time", columnList = "check_time")
})
public class ApiMonitorRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 接口ID
     */
    @Column(name = "api_id", nullable = false)
    private Long apiId;

    /**
     * 状态: SUCCESS, ERROR, TIMEOUT, NETWORK_ERROR
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status;

    /**
     * 响应时间(毫秒)
     */
    @Column(name = "response_time")
    private Integer responseTime;

    /**
     * HTTP状态码
     */
    @Column(name = "http_status")
    private Integer httpStatus;

    /**
     * 业务错误码
     */
    @Column(name = "error_code", length = 50)
    private String errorCode;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 响应内容(截取前1000字符)
     */
    @Column(name = "response_content", length = 1000)
    private String responseContent;

    /**
     * 检查时间
     */
    @CreationTimestamp
    @Column(name = "check_time", updatable = false)
    private LocalDateTime checkTime;

    /**
     * 检查类型: SCHEDULED(定时检查), MANUAL(手动检查), INTERCEPTOR(拦截器记录)
     */
    @Column(name = "check_type", length = 20)
    private String checkType = "SCHEDULED";
}
