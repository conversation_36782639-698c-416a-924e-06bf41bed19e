package com.monitor.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * API配置实体类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "api_config")
public class ApiConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 接口名称
     */
    @Column(name = "api_name", nullable = false, length = 100)
    private String apiName;

    /**
     * 接口路径
     */
    @Column(name = "api_path", nullable = false, length = 200)
    private String apiPath;

    /**
     * 请求方法
     */
    @Column(name = "api_method", length = 10)
    private String apiMethod = "GET";

    /**
     * 超时时间(毫秒)
     */
    @Column(name = "timeout_ms")
    private Integer timeoutMs = 5000;

    /**
     * 检查间隔(秒)
     */
    @Column(name = "check_interval")
    private Integer checkInterval = 60;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled = true;

    /**
     * 请求头信息(JSON格式)
     */
    @Column(name = "headers", columnDefinition = "TEXT")
    private String headers;

    /**
     * 请求体(JSON格式)
     */
    @Column(name = "request_body", columnDefinition = "TEXT")
    private String requestBody;

    /**
     * 期望的响应内容(用于验证)
     */
    @Column(name = "expected_response", columnDefinition = "TEXT")
    private String expectedResponse;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_time", updatable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
}
