package com.monitor.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * API统计数据实体类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "api_statistics", 
       uniqueConstraints = @UniqueConstraint(name = "uk_api_date", columnNames = {"apiId", "statDate"}))
public class ApiStatistics {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 接口ID
     */
    @Column(name = "api_id", nullable = false)
    private Long apiId;

    /**
     * 统计日期
     */
    @Column(name = "stat_date", nullable = false)
    private LocalDate statDate;

    /**
     * 总调用次数
     */
    @Column(name = "total_count")
    private Integer totalCount = 0;

    /**
     * 成功次数
     */
    @Column(name = "success_count")
    private Integer successCount = 0;

    /**
     * 错误次数
     */
    @Column(name = "error_count")
    private Integer errorCount = 0;

    /**
     * 平均响应时间
     */
    @Column(name = "avg_response_time", precision = 10, scale = 2)
    private BigDecimal avgResponseTime;

    /**
     * 最大响应时间
     */
    @Column(name = "max_response_time")
    private Integer maxResponseTime;

    /**
     * 最小响应时间
     */
    @Column(name = "min_response_time")
    private Integer minResponseTime;

    /**
     * 错误率
     */
    @Column(name = "error_rate", precision = 5, scale = 2)
    private BigDecimal errorRate;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_time", updatable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
}
