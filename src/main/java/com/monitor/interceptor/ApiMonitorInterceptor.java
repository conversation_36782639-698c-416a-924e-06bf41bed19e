package com.monitor.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.monitor.entity.ApiConfig;
import com.monitor.entity.ApiMonitorRecord;
import com.monitor.repository.ApiConfigRepository;
import com.monitor.service.ApiMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * API监控拦截器
 * 用于拦截和记录API请求的监控数据
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
public class ApiMonitorInterceptor implements HandlerInterceptor {

    @Autowired
    private ApiConfigRepository apiConfigRepository;

    @Autowired
    private ApiMonitorService apiMonitorService;

    private static final String START_TIME_ATTRIBUTE = "startTime";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 记录请求开始时间
        request.setAttribute(START_TIME_ATTRIBUTE, System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        try {
            // 跳过监控系统自身的请求
            String requestPath = request.getRequestURI();
            if (isMonitorSystemPath(requestPath)) {
                return;
            }

            // 查找对应的API配置
            String method = request.getMethod();
            Optional<ApiConfig> configOpt = apiConfigRepository.findByApiPathAndApiMethod(requestPath, method);
            
            if (!configOpt.isPresent()) {
                // 如果没有配置，尝试只根据路径查找
                configOpt = apiConfigRepository.findByApiPath(requestPath);
            }

            if (configOpt.isPresent()) {
                ApiConfig config = configOpt.get();
                if (config.getIsEnabled()) {
                    recordApiCall(request, response, config, ex);
                }
            }
        } catch (Exception e) {
            log.error("API监控拦截器处理异常", e);
        }
    }

    /**
     * 记录API调用信息
     */
    private void recordApiCall(HttpServletRequest request, HttpServletResponse response, 
                              ApiConfig config, Exception ex) {
        ApiMonitorRecord record = new ApiMonitorRecord();
        record.setApiId(config.getId());
        record.setCheckType("INTERCEPTOR");
        record.setCheckTime(LocalDateTime.now());

        // 计算响应时间
        Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
        if (startTime != null) {
            long responseTime = System.currentTimeMillis() - startTime;
            record.setResponseTime((int) responseTime);
        }

        // 设置HTTP状态码
        record.setHttpStatus(response.getStatus());

        // 判断请求状态
        if (ex != null) {
            record.setStatus("ERROR");
            record.setErrorMessage("系统异常: " + ex.getMessage());
        } else if (response.getStatus() >= 400) {
            record.setStatus("ERROR");
            record.setErrorMessage("HTTP错误: " + response.getStatus());
        } else {
            // 检查响应内容中的业务错误码
            String responseBody = getResponseBody(response);
            if (StrUtil.isNotBlank(responseBody) && checkBusinessError(responseBody)) {
                record.setStatus("ERROR");
                try {
                    JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                    record.setErrorCode(jsonResponse.getStr("code"));
                    record.setErrorMessage(jsonResponse.getStr("msg"));
                } catch (Exception e) {
                    record.setErrorMessage("业务错误");
                }
            } else {
                record.setStatus("SUCCESS");
            }
        }

        // 异步记录监控结果
        try {
            apiMonitorService.recordMonitorResult(record);
        } catch (Exception e) {
            log.error("记录API监控结果失败", e);
        }
    }

    /**
     * 检查是否为监控系统路径
     */
    private boolean isMonitorSystemPath(String path) {
        return path.startsWith("/api-monitor/monitor") ||
               path.startsWith("/api-monitor/static") ||
               path.startsWith("/api-monitor/css") ||
               path.startsWith("/api-monitor/js") ||
               path.startsWith("/api-monitor/images") ||
               path.startsWith("/api-monitor/druid") ||
               path.startsWith("/api-monitor/actuator");
    }

    /**
     * 获取响应体内容
     * 注意：这里需要使用ResponseWrapper来获取响应内容
     */
    private String getResponseBody(HttpServletResponse response) {
        // 由于HttpServletResponse的限制，这里暂时返回空
        // 在实际项目中，可以使用ResponseWrapper来包装响应以获取内容
        return null;
    }

    /**
     * 检查业务错误码
     */
    private boolean checkBusinessError(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            return false;
        }

        try {
            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
            if (jsonResponse.containsKey("code")) {
                String code = jsonResponse.getStr("code");
                // 检查是否为业务错误码
                return !("0".equals(code) || "200".equals(code) || "success".equalsIgnoreCase(code));
            }
        } catch (Exception e) {
            // 非JSON响应，不视为业务错误
            return false;
        }

        return false;
    }
}
