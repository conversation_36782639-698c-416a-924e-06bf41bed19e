package com.monitor.task;

import com.monitor.service.ApiMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 监控定时任务
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
public class MonitorScheduledTask {

    @Autowired
    private ApiMonitorService apiMonitorService;

    /**
     * 定时检查所有启用的API
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void checkAllApis() {
        try {
            log.debug("开始执行定时API检查任务");
            apiMonitorService.checkAllEnabledApis();
        } catch (Exception e) {
            log.error("定时API检查任务执行失败", e);
        }
    }

    /**
     * 更新统计数据
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    public void updateStatistics() {
        try {
            log.debug("开始更新统计数据");
            apiMonitorService.updateStatistics();
        } catch (Exception e) {
            log.error("更新统计数据失败", e);
        }
    }

    /**
     * 清理历史数据
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupHistoryData() {
        try {
            log.info("开始清理历史数据");
            apiMonitorService.cleanupHistoryData();
            log.info("历史数据清理完成");
        } catch (Exception e) {
            log.error("清理历史数据失败", e);
        }
    }
}
