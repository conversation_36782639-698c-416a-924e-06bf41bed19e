package com.monitor.controller;

import com.monitor.dto.ApiDetailDTO;
import com.monitor.dto.ApiMonitorDTO;
import com.monitor.entity.ApiConfig;
import com.monitor.entity.ApiMonitorRecord;
import com.monitor.service.ApiMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监控页面控制器
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Controller
@RequestMapping("/monitor")
public class MonitorController {

    @Autowired
    private ApiMonitorService apiMonitorService;

    /**
     * 监控首页
     */
    @GetMapping({"", "/", "/index"})
    public String index(Model model) {
        try {
            List<ApiMonitorDTO> monitorList = apiMonitorService.getAllApiMonitorOverview();
            model.addAttribute("monitorList", monitorList);
            model.addAttribute("totalCount", monitorList.size());
            
            // 统计各状态数量
            long successCount = monitorList.stream().filter(m -> "SUCCESS".equals(m.getStatus())).count();
            long errorCount = monitorList.stream().filter(m -> "ERROR".equals(m.getStatus())).count();
            long timeoutCount = monitorList.stream().filter(m -> "TIMEOUT".equals(m.getStatus())).count();
            long unknownCount = monitorList.stream().filter(m -> "UNKNOWN".equals(m.getStatus())).count();
            
            model.addAttribute("successCount", successCount);
            model.addAttribute("errorCount", errorCount);
            model.addAttribute("timeoutCount", timeoutCount);
            model.addAttribute("unknownCount", unknownCount);
            
        } catch (Exception e) {
            log.error("获取监控概览失败", e);
            model.addAttribute("error", "获取监控数据失败: " + e.getMessage());
        }
        
        return "monitor/index";
    }

    /**
     * API详情页面
     */
    @GetMapping("/detail/{apiId}")
    public String detail(@PathVariable Long apiId, Model model) {
        try {
            ApiDetailDTO detail = apiMonitorService.getApiDetail(apiId);
            model.addAttribute("detail", detail);
        } catch (Exception e) {
            log.error("获取API详情失败: {}", apiId, e);
            model.addAttribute("error", "获取API详情失败: " + e.getMessage());
        }
        
        return "monitor/detail";
    }

    /**
     * API配置管理页面
     */
    @GetMapping("/config")
    public String config(Model model) {
        try {
            List<ApiConfig> configs = apiMonitorService.getAllApiConfigs();
            model.addAttribute("configs", configs);
        } catch (Exception e) {
            log.error("获取API配置失败", e);
            model.addAttribute("error", "获取API配置失败: " + e.getMessage());
        }
        
        return "monitor/config";
    }

    /**
     * 添加/编辑API配置页面
     */
    @GetMapping("/config/edit")
    public String editConfig(@RequestParam(required = false) Long id, Model model) {
        ApiConfig config = new ApiConfig();
        if (id != null) {
            // 编辑模式，获取现有配置
            List<ApiConfig> configs = apiMonitorService.getAllApiConfigs();
            config = configs.stream()
                    .filter(c -> c.getId().equals(id))
                    .findFirst()
                    .orElse(new ApiConfig());
        }
        model.addAttribute("config", config);
        model.addAttribute("isEdit", id != null);
        
        return "monitor/config-edit";
    }

    /**
     * 获取监控数据API
     */
    @GetMapping("/api/overview")
    @ResponseBody
    public ResponseEntity<List<ApiMonitorDTO>> getMonitorOverview() {
        try {
            List<ApiMonitorDTO> result = apiMonitorService.getAllApiMonitorOverview();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取监控概览API失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 手动检查API
     */
    @PostMapping("/api/check/{apiId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> manualCheck(@PathVariable Long apiId) {
        Map<String, Object> result = new HashMap<>();
        try {
            ApiMonitorRecord record = apiMonitorService.manualCheckApi(apiId);
            result.put("success", true);
            result.put("message", "检查完成");
            result.put("status", record.getStatus());
            result.put("responseTime", record.getResponseTime());
            result.put("errorMessage", record.getErrorMessage());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("手动检查API失败: {}", apiId, e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 保存API配置
     */
    @PostMapping("/api/config/save")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> saveConfig(@RequestBody ApiConfig config) {
        Map<String, Object> result = new HashMap<>();
        try {
            ApiConfig saved = apiMonitorService.saveApiConfig(config);
            result.put("success", true);
            result.put("message", "保存成功");
            result.put("data", saved);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("保存API配置失败", e);
            result.put("success", false);
            result.put("message", "保存失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 删除API配置
     */
    @DeleteMapping("/api/config/{apiId}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long apiId) {
        Map<String, Object> result = new HashMap<>();
        try {
            apiMonitorService.deleteApiConfig(apiId);
            result.put("success", true);
            result.put("message", "删除成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除API配置失败: {}", apiId, e);
            result.put("success", false);
            result.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 启用/禁用API监控
     */
    @PostMapping("/api/config/{apiId}/toggle")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> toggleEnabled(@PathVariable Long apiId, 
                                                           @RequestParam Boolean enabled) {
        Map<String, Object> result = new HashMap<>();
        try {
            apiMonitorService.toggleApiEnabled(apiId, enabled);
            result.put("success", true);
            result.put("message", enabled ? "已启用" : "已禁用");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("切换API状态失败: {}", apiId, e);
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
}
