package com.monitor.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 测试API控制器
 * 用于演示监控功能
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestApiController {

    private final Random random = new Random();

    /**
     * 正常接口 - 总是返回成功
     */
    @GetMapping("/success")
    public Map<String, Object> success() {
        log.info("调用成功接口");
        
        // 模拟不同的响应时间
        try {
            Thread.sleep(random.nextInt(200) + 50); // 50-250ms
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", "接口调用成功");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 随机接口 - 随机返回成功或失败
     */
    @GetMapping("/random")
    public Map<String, Object> randomResult() {
        log.info("调用随机接口");
        
        // 模拟响应时间
        try {
            Thread.sleep(random.nextInt(300) + 100); // 100-400ms
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // 70%概率成功，30%概率失败
        if (random.nextDouble() < 0.7) {
            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", "随机接口调用成功");
        } else {
            result.put("code", -1001);
            result.put("msg", "random error occurred");
            result.put("data", null);
        }
        
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 业务错误接口 - 返回业务错误码
     */
    @GetMapping("/business-error")
    public Map<String, Object> businessError() {
        log.info("调用业务错误接口");
        
        try {
            Thread.sleep(random.nextInt(100) + 50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", -10014);
        result.put("msg", "api request not allowed");
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 慢接口 - 模拟响应时间较长的接口
     */
    @GetMapping("/slow")
    public Map<String, Object> slowApi() {
        log.info("调用慢接口");
        
        try {
            // 模拟2-5秒的响应时间
            Thread.sleep(random.nextInt(3000) + 2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", "慢接口调用成功");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 异常接口 - 抛出运行时异常
     */
    @GetMapping("/exception")
    public Map<String, Object> exceptionApi() {
        log.info("调用异常接口");
        
        // 50%概率抛出异常
        if (random.nextBoolean()) {
            throw new RuntimeException("模拟系统异常");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", "异常接口调用成功");
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * POST接口 - 用于测试POST请求监控
     */
    @PostMapping("/post-test")
    public Map<String, Object> postTest(@RequestBody(required = false) Map<String, Object> requestData) {
        log.info("调用POST测试接口，请求数据: {}", requestData);
        
        try {
            Thread.sleep(random.nextInt(200) + 100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", "POST接口调用成功");
        result.put("requestData", requestData);
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 用户信息接口 - 模拟真实业务接口
     */
    @GetMapping("/user/{userId}")
    public Map<String, Object> getUserInfo(@PathVariable Long userId) {
        log.info("查询用户信息: {}", userId);
        
        try {
            Thread.sleep(random.nextInt(150) + 50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> result = new HashMap<>();
        
        // 模拟用户不存在的情况
        if (userId <= 0 || userId > 1000) {
            result.put("code", -2001);
            result.put("msg", "user not found");
            result.put("data", null);
        } else {
            Map<String, Object> userData = new HashMap<>();
            userData.put("userId", userId);
            userData.put("username", "user_" + userId);
            userData.put("email", "user" + userId + "@example.com");
            userData.put("status", "active");
            
            result.put("code", 200);
            result.put("msg", "success");
            result.put("data", userData);
        }
        
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("version", "1.0.0");
        
        return result;
    }
}
