package com.monitor.service;

import com.monitor.dto.ApiDetailDTO;
import com.monitor.dto.ApiMonitorDTO;
import com.monitor.entity.ApiConfig;
import com.monitor.entity.ApiMonitorRecord;

import java.util.List;

/**
 * API监控服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ApiMonitorService {

    /**
     * 获取所有API监控概览
     */
    List<ApiMonitorDTO> getAllApiMonitorOverview();

    /**
     * 获取API详情信息
     */
    ApiDetailDTO getApiDetail(Long apiId);

    /**
     * 手动检查API状态
     */
    ApiMonitorRecord manualCheckApi(Long apiId);

    /**
     * 检查单个API
     */
    ApiMonitorRecord checkSingleApi(ApiConfig apiConfig);

    /**
     * 批量检查所有启用的API
     */
    void checkAllEnabledApis();

    /**
     * 记录监控结果
     */
    void recordMonitorResult(ApiMonitorRecord record);

    /**
     * 更新统计数据
     */
    void updateStatistics();

    /**
     * 清理历史数据
     */
    void cleanupHistoryData();

    /**
     * 获取API配置列表
     */
    List<ApiConfig> getAllApiConfigs();

    /**
     * 保存API配置
     */
    ApiConfig saveApiConfig(ApiConfig apiConfig);

    /**
     * 删除API配置
     */
    void deleteApiConfig(Long apiId);

    /**
     * 启用/禁用API监控
     */
    void toggleApiEnabled(Long apiId, Boolean enabled);
}
