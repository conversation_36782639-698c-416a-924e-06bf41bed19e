package com.monitor.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.monitor.dto.ApiDetailDTO;
import com.monitor.dto.ApiMonitorDTO;
import com.monitor.entity.ApiConfig;
import com.monitor.entity.ApiMonitorRecord;
import com.monitor.entity.ApiStatistics;
import com.monitor.repository.ApiConfigRepository;
import com.monitor.repository.ApiMonitorRecordRepository;
import com.monitor.repository.ApiStatisticsRepository;
import com.monitor.service.ApiMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * API监控服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
public class ApiMonitorServiceImpl implements ApiMonitorService {

    @Autowired
    private ApiConfigRepository apiConfigRepository;

    @Autowired
    private ApiMonitorRecordRepository recordRepository;

    @Autowired
    private ApiStatisticsRepository statisticsRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${monitor.default-timeout:5000}")
    private Integer defaultTimeout;

    @Value("${monitor.cache.statistics-ttl:300}")
    private Long statisticsTtl;

    private static final String CACHE_KEY_PREFIX = "api_monitor:";
    private static final String CACHE_KEY_OVERVIEW = CACHE_KEY_PREFIX + "overview";
    private static final String CACHE_KEY_DETAIL = CACHE_KEY_PREFIX + "detail:";

    @Override
    public List<ApiMonitorDTO> getAllApiMonitorOverview() {
        // 尝试从缓存获取
        List<ApiMonitorDTO> cachedResult = (List<ApiMonitorDTO>) redisTemplate.opsForValue()
                .get(CACHE_KEY_OVERVIEW);
        if (cachedResult != null) {
            return cachedResult;
        }

        List<ApiConfig> apiConfigs = apiConfigRepository.findAll();
        List<ApiMonitorDTO> result = new ArrayList<>();

        for (ApiConfig config : apiConfigs) {
            ApiMonitorDTO dto = buildApiMonitorDTO(config);
            result.add(dto);
        }

        // 缓存结果
        redisTemplate.opsForValue().set(CACHE_KEY_OVERVIEW, result, statisticsTtl, TimeUnit.SECONDS);

        return result;
    }

    @Override
    public ApiDetailDTO getApiDetail(Long apiId) {
        String cacheKey = CACHE_KEY_DETAIL + apiId;
        ApiDetailDTO cachedResult = (ApiDetailDTO) redisTemplate.opsForValue().get(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        Optional<ApiConfig> configOpt = apiConfigRepository.findById(apiId);
        if (!configOpt.isPresent()) {
            throw new RuntimeException("API配置不存在: " + apiId);
        }

        ApiConfig config = configOpt.get();
        ApiDetailDTO detail = buildApiDetailDTO(config);

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, detail, statisticsTtl, TimeUnit.SECONDS);

        return detail;
    }

    @Override
    public ApiMonitorRecord manualCheckApi(Long apiId) {
        Optional<ApiConfig> configOpt = apiConfigRepository.findById(apiId);
        if (!configOpt.isPresent()) {
            throw new RuntimeException("API配置不存在: " + apiId);
        }

        ApiMonitorRecord record = checkSingleApi(configOpt.get());
        record.setCheckType("MANUAL");
        recordMonitorResult(record);

        // 清除缓存
        clearCache(apiId);

        return record;
    }

    @Override
    public ApiMonitorRecord checkSingleApi(ApiConfig apiConfig) {
        ApiMonitorRecord record = new ApiMonitorRecord();
        record.setApiId(apiConfig.getId());
        record.setCheckTime(LocalDateTime.now());

        long startTime = System.currentTimeMillis();

        try {
            HttpRequest request = buildHttpRequest(apiConfig);
            HttpResponse response = request.execute();

            long responseTime = System.currentTimeMillis() - startTime;
            record.setResponseTime((int) responseTime);
            record.setHttpStatus(response.getStatus());

            String responseBody = response.body();
            record.setResponseContent(StrUtil.sub(responseBody, 0, 1000));

            // 检查响应状态
            if (response.isOk()) {
                // 检查业务错误码
                if (checkBusinessError(responseBody)) {
                    record.setStatus("ERROR");
                    JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                    record.setErrorCode(jsonResponse.getStr("code"));
                    record.setErrorMessage(jsonResponse.getStr("msg"));
                } else {
                    record.setStatus("SUCCESS");
                }
            } else {
                record.setStatus("ERROR");
                record.setErrorMessage("HTTP错误: " + response.getStatus());
            }

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            record.setResponseTime((int) responseTime);

            if (responseTime >= apiConfig.getTimeoutMs()) {
                record.setStatus("TIMEOUT");
                record.setErrorMessage("请求超时");
            } else {
                record.setStatus("NETWORK_ERROR");
                record.setErrorMessage("网络异常: " + e.getMessage());
            }

            log.error("检查API失败: {} - {}", apiConfig.getApiPath(), e.getMessage());
        }

        return record;
    }

    @Override
    @Async
    public void checkAllEnabledApis() {
        List<ApiConfig> enabledApis = apiConfigRepository.findByIsEnabledTrue();
        log.info("开始检查 {} 个启用的API", enabledApis.size());

        for (ApiConfig apiConfig : enabledApis) {
            try {
                ApiMonitorRecord record = checkSingleApi(apiConfig);
                record.setCheckType("SCHEDULED");
                recordMonitorResult(record);
            } catch (Exception e) {
                log.error("检查API异常: {} - {}", apiConfig.getApiPath(), e.getMessage());
            }
        }

        // 清除概览缓存
        redisTemplate.delete(CACHE_KEY_OVERVIEW);
        log.info("完成API检查任务");
    }

    @Override
    @Transactional
    public void recordMonitorResult(ApiMonitorRecord record) {
        recordRepository.save(record);
        log.debug("记录监控结果: API={}, Status={}, ResponseTime={}ms", 
                 record.getApiId(), record.getStatus(), record.getResponseTime());
    }

    private ApiMonitorDTO buildApiMonitorDTO(ApiConfig config) {
        ApiMonitorDTO dto = new ApiMonitorDTO();
        dto.setApiId(config.getId());
        dto.setApiName(config.getApiName());
        dto.setApiPath(config.getApiPath());
        dto.setIsEnabled(config.getIsEnabled());

        // 获取最新状态
        List<ApiMonitorRecord> latestRecords = recordRepository.findLatestByApiId(
                config.getId(), PageRequest.of(0, 1));
        if (!latestRecords.isEmpty()) {
            ApiMonitorRecord latest = latestRecords.get(0);
            dto.setStatus(latest.getStatus());
            dto.setLastCheckTime(latest.getCheckTime());
            dto.setLastErrorMessage(latest.getErrorMessage());
        } else {
            dto.setStatus("UNKNOWN");
            dto.setStatusDesc("未检查");
        }

        // 获取今日统计
        dto.setTodayCount(recordRepository.countTodayRecords(config.getId()));
        Double avgResponseTime = recordRepository.calculateAvgResponseTime(config.getId());
        if (avgResponseTime != null) {
            dto.setAvgResponseTime(BigDecimal.valueOf(avgResponseTime).setScale(2, RoundingMode.HALF_UP));
        }

        // 计算错误率
        long todayTotal = dto.getTodayCount();
        long todaySuccess = recordRepository.countTodaySuccessRecords(config.getId());
        if (todayTotal > 0) {
            BigDecimal errorRate = BigDecimal.valueOf((todayTotal - todaySuccess) * 100.0 / todayTotal)
                    .setScale(2, RoundingMode.HALF_UP);
            dto.setErrorRate(errorRate);
        } else {
            dto.setErrorRate(BigDecimal.ZERO);
        }

        return dto;
    }

    private ApiDetailDTO buildApiDetailDTO(ApiConfig config) {
        ApiDetailDTO detail = new ApiDetailDTO();
        
        // 基本信息
        detail.setApiId(config.getId());
        detail.setApiName(config.getApiName());
        detail.setApiPath(config.getApiPath());
        detail.setApiMethod(config.getApiMethod());
        detail.setTimeoutMs(config.getTimeoutMs());
        detail.setCheckInterval(config.getCheckInterval());
        detail.setIsEnabled(config.getIsEnabled());

        // 当前状态
        List<ApiMonitorRecord> latestRecords = recordRepository.findLatestByApiId(
                config.getId(), PageRequest.of(0, 1));
        if (!latestRecords.isEmpty()) {
            ApiMonitorRecord latest = latestRecords.get(0);
            detail.setCurrentStatus(latest.getStatus());
            detail.setLastCheckTime(latest.getCheckTime());
            detail.setLastErrorMessage(latest.getErrorMessage());
            detail.setLastResponseTime(latest.getResponseTime());
        }

        // 今日统计
        detail.setTodayTotalCount(recordRepository.countTodayRecords(config.getId()));
        detail.setTodaySuccessCount(recordRepository.countTodaySuccessRecords(config.getId()));
        detail.setTodayErrorCount(detail.getTodayTotalCount() - detail.getTodaySuccessCount());
        
        if (detail.getTodayTotalCount() > 0) {
            BigDecimal errorRate = BigDecimal.valueOf(detail.getTodayErrorCount() * 100.0 / detail.getTodayTotalCount())
                    .setScale(2, RoundingMode.HALF_UP);
            detail.setTodayErrorRate(errorRate);
        }

        Double avgResponseTime = recordRepository.calculateAvgResponseTime(config.getId());
        if (avgResponseTime != null) {
            detail.setTodayAvgResponseTime(BigDecimal.valueOf(avgResponseTime).setScale(2, RoundingMode.HALF_UP));
        }

        // 最近记录
        List<ApiMonitorRecord> recentRecords = recordRepository.findLatestByApiId(
                config.getId(), PageRequest.of(0, 20));
        detail.setRecentRecords(recentRecords.stream()
                .map(this::convertToRecordData)
                .collect(Collectors.toList()));

        return detail;
    }

    private ApiDetailDTO.MonitorRecordData convertToRecordData(ApiMonitorRecord record) {
        ApiDetailDTO.MonitorRecordData data = new ApiDetailDTO.MonitorRecordData();
        data.setId(record.getId());
        data.setStatus(record.getStatus());
        data.setResponseTime(record.getResponseTime());
        data.setHttpStatus(record.getHttpStatus());
        data.setErrorCode(record.getErrorCode());
        data.setErrorMessage(record.getErrorMessage());
        data.setResponseContent(record.getResponseContent());
        data.setCheckTime(record.getCheckTime());
        data.setCheckType(record.getCheckType());
        return data;
    }

    private HttpRequest buildHttpRequest(ApiConfig apiConfig) {
        HttpRequest request;
        
        if ("POST".equalsIgnoreCase(apiConfig.getApiMethod())) {
            request = HttpRequest.post(apiConfig.getApiPath());
            if (StrUtil.isNotBlank(apiConfig.getRequestBody())) {
                request.body(apiConfig.getRequestBody());
            }
        } else {
            request = HttpRequest.get(apiConfig.getApiPath());
        }

        request.timeout(apiConfig.getTimeoutMs() != null ? apiConfig.getTimeoutMs() : defaultTimeout);

        // 添加请求头
        if (StrUtil.isNotBlank(apiConfig.getHeaders())) {
            try {
                JSONObject headers = JSONUtil.parseObj(apiConfig.getHeaders());
                headers.forEach((key, value) -> request.header(key, value.toString()));
            } catch (Exception e) {
                log.warn("解析请求头失败: {}", e.getMessage());
            }
        }

        return request;
    }

    private boolean checkBusinessError(String responseBody) {
        if (StrUtil.isBlank(responseBody)) {
            return false;
        }

        try {
            JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
            if (jsonResponse.containsKey("code")) {
                String code = jsonResponse.getStr("code");
                // 检查是否为业务错误码
                return !("0".equals(code) || "200".equals(code) || "success".equalsIgnoreCase(code));
            }
        } catch (Exception e) {
            // 非JSON响应，不视为业务错误
            return false;
        }

        return false;
    }

    private void clearCache(Long apiId) {
        redisTemplate.delete(CACHE_KEY_OVERVIEW);
        redisTemplate.delete(CACHE_KEY_DETAIL + apiId);
    }

    @Override
    public void updateStatistics() {
        // 实现统计数据更新逻辑
        log.info("更新统计数据");
    }

    @Override
    public void cleanupHistoryData() {
        // 实现历史数据清理逻辑
        log.info("清理历史数据");
    }

    @Override
    public List<ApiConfig> getAllApiConfigs() {
        return apiConfigRepository.findAll();
    }

    @Override
    @Transactional
    public ApiConfig saveApiConfig(ApiConfig apiConfig) {
        ApiConfig saved = apiConfigRepository.save(apiConfig);
        clearCache(saved.getId());
        return saved;
    }

    @Override
    @Transactional
    public void deleteApiConfig(Long apiId) {
        apiConfigRepository.deleteById(apiId);
        clearCache(apiId);
    }

    @Override
    @Transactional
    public void toggleApiEnabled(Long apiId, Boolean enabled) {
        Optional<ApiConfig> configOpt = apiConfigRepository.findById(apiId);
        if (configOpt.isPresent()) {
            ApiConfig config = configOpt.get();
            config.setIsEnabled(enabled);
            apiConfigRepository.save(config);
            clearCache(apiId);
        }
    }
}
