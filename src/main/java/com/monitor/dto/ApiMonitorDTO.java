package com.monitor.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * API监控数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ApiMonitorDTO {

    /**
     * 接口ID
     */
    private Long apiId;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 接口路径
     */
    private String apiPath;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 今日调用量
     */
    private Long todayCount;

    /**
     * 平均响应时间(毫秒)
     */
    private BigDecimal avgResponseTime;

    /**
     * 错误率(%)
     */
    private BigDecimal errorRate;

    /**
     * 最后检查时间
     */
    private LocalDateTime lastCheckTime;

    /**
     * 最后错误信息
     */
    private String lastErrorMessage;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 连续失败次数
     */
    private Integer consecutiveFailures;

    /**
     * 获取状态样式类
     */
    public String getStatusClass() {
        if (status == null) {
            return "secondary";
        }
        switch (status.toUpperCase()) {
            case "SUCCESS":
                return "success";
            case "ERROR":
                return "danger";
            case "TIMEOUT":
                return "warning";
            case "NETWORK_ERROR":
                return "dark";
            default:
                return "secondary";
        }
    }

    /**
     * 获取状态图标
     */
    public String getStatusIcon() {
        if (status == null) {
            return "fas fa-question-circle";
        }
        switch (status.toUpperCase()) {
            case "SUCCESS":
                return "fas fa-check-circle";
            case "ERROR":
                return "fas fa-times-circle";
            case "TIMEOUT":
                return "fas fa-clock";
            case "NETWORK_ERROR":
                return "fas fa-exclamation-triangle";
            default:
                return "fas fa-question-circle";
        }
    }
}
