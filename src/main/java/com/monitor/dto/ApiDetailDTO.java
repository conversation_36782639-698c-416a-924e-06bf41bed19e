package com.monitor.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * API详情数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
public class ApiDetailDTO {

    /**
     * 接口基本信息
     */
    private Long apiId;
    private String apiName;
    private String apiPath;
    private String apiMethod;
    private Integer timeoutMs;
    private Integer checkInterval;
    private Boolean isEnabled;

    /**
     * 当前状态信息
     */
    private String currentStatus;
    private String statusDesc;
    private LocalDateTime lastCheckTime;
    private String lastErrorMessage;
    private Integer lastResponseTime;

    /**
     * 今日统计数据
     */
    private Long todayTotalCount;
    private Long todaySuccessCount;
    private Long todayErrorCount;
    private BigDecimal todayErrorRate;
    private BigDecimal todayAvgResponseTime;

    /**
     * 历史统计数据
     */
    private List<StatisticsData> historyStatistics;

    /**
     * 最近监控记录
     */
    private List<MonitorRecordData> recentRecords;

    /**
     * 错误记录
     */
    private List<MonitorRecordData> errorRecords;

    /**
     * 统计数据内部类
     */
    @Data
    public static class StatisticsData {
        private String date;
        private Integer totalCount;
        private Integer successCount;
        private Integer errorCount;
        private BigDecimal errorRate;
        private BigDecimal avgResponseTime;
    }

    /**
     * 监控记录内部类
     */
    @Data
    public static class MonitorRecordData {
        private Long id;
        private String status;
        private Integer responseTime;
        private Integer httpStatus;
        private String errorCode;
        private String errorMessage;
        private String responseContent;
        private LocalDateTime checkTime;
        private String checkType;

        /**
         * 获取状态样式类
         */
        public String getStatusClass() {
            if (status == null) {
                return "secondary";
            }
            switch (status.toUpperCase()) {
                case "SUCCESS":
                    return "success";
                case "ERROR":
                    return "danger";
                case "TIMEOUT":
                    return "warning";
                case "NETWORK_ERROR":
                    return "dark";
                default:
                    return "secondary";
            }
        }
    }

    /**
     * 获取状态样式类
     */
    public String getStatusClass() {
        if (currentStatus == null) {
            return "secondary";
        }
        switch (currentStatus.toUpperCase()) {
            case "SUCCESS":
                return "success";
            case "ERROR":
                return "danger";
            case "TIMEOUT":
                return "warning";
            case "NETWORK_ERROR":
                return "dark";
            default:
                return "secondary";
        }
    }

    /**
     * 获取状态图标
     */
    public String getStatusIcon() {
        if (currentStatus == null) {
            return "fas fa-question-circle";
        }
        switch (currentStatus.toUpperCase()) {
            case "SUCCESS":
                return "fas fa-check-circle";
            case "ERROR":
                return "fas fa-times-circle";
            case "TIMEOUT":
                return "fas fa-clock";
            case "NETWORK_ERROR":
                return "fas fa-exclamation-triangle";
            default:
                return "fas fa-question-circle";
        }
    }
}
