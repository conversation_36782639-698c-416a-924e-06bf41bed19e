# API接口监控系统部署指南

## 系统概述

本系统是一个基于Spring Boot开发的API接口监控系统，具备以下特性：

- **实时监控**: 定时检查API接口状态，支持HTTP状态码和业务错误码检测
- **可视化界面**: 提供直观的监控概览和详情页面
- **统计分析**: 自动生成调用量、响应时间、错误率等统计数据
- **灵活配置**: 支持动态添加、编辑、启用/禁用监控接口
- **异常记录**: 详细记录接口异常信息，便于问题排查

## 部署环境要求

### 基础环境
- **操作系统**: Linux/Windows/macOS
- **JDK**: 8或更高版本
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 6.0+

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 2GB以上
- **磁盘**: 10GB以上可用空间

## 快速部署步骤

### 1. 环境准备

#### 安装MySQL
```bash
# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld

# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### 安装Redis
```bash
# CentOS/RHEL
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis

# Ubuntu/Debian
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 2. 数据库初始化

```bash
# 登录MySQL
mysql -u root -p

# 执行初始化脚本
source /path/to/project/src/main/resources/sql/init.sql
```

### 3. 配置文件修改

编辑 `src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: your_mysql_username
    password: your_mysql_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password  # 如果Redis设置了密码
```

### 4. 编译和启动

```bash
# 编译项目
mvn clean package -DskipTests

# 使用启动脚本
./start.sh start

# 或者直接运行jar包
java -jar target/api-monitor-1.0.0.jar
```

### 5. 验证部署

访问以下地址验证部署是否成功：

- **监控系统**: http://localhost:8080/api-monitor/monitor
- **数据库监控**: http://localhost:8080/api-monitor/druid
- **健康检查**: http://localhost:8080/api-monitor/actuator/health

## 详细配置说明

### 数据库配置

```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      initial-size: 5          # 初始连接数
      min-idle: 5              # 最小空闲连接数
      max-active: 20           # 最大连接数
      max-wait: 60000          # 获取连接等待超时时间
      validation-query: SELECT 1 FROM DUAL
```

### Redis配置

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
```

### 监控系统配置

```yaml
monitor:
  # 默认超时时间(毫秒)
  default-timeout: 5000
  # 默认检查间隔(秒)
  default-check-interval: 60
  # 线程池配置
  thread-pool:
    core-size: 5
    max-size: 10
    queue-capacity: 100
    keep-alive-seconds: 60
  # 缓存配置
  cache:
    # 统计数据缓存时间(秒)
    statistics-ttl: 300
    # 接口配置缓存时间(秒)
    config-ttl: 600
  # 数据清理配置
  cleanup:
    # 保留监控记录天数
    keep-record-days: 30
    # 保留统计数据天数
    keep-statistics-days: 90
    # 清理任务执行时间(cron表达式)
    cron: "0 0 2 * * ?"
```

## 生产环境部署

### 1. 系统服务配置

创建systemd服务文件 `/etc/systemd/system/api-monitor.service`：

```ini
[Unit]
Description=API Monitor System
After=network.target mysql.service redis.service

[Service]
Type=forking
User=apimonitor
Group=apimonitor
WorkingDirectory=/opt/api-monitor
ExecStart=/opt/api-monitor/start.sh start
ExecStop=/opt/api-monitor/start.sh stop
ExecReload=/opt/api-monitor/start.sh restart
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable api-monitor
sudo systemctl start api-monitor
```

### 2. Nginx反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api-monitor/ {
        proxy_pass http://localhost:8080/api-monitor/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 日志管理

配置logrotate进行日志轮转，创建 `/etc/logrotate.d/api-monitor`：

```
/opt/api-monitor/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 apimonitor apimonitor
    postrotate
        systemctl reload api-monitor
    endscript
}
```

## 使用指南

### 添加监控接口

1. 访问配置管理页面：http://localhost:8080/api-monitor/monitor/config
2. 点击"添加接口"按钮
3. 填写接口信息：
   - **接口名称**: 便于识别的业务名称
   - **接口路径**: 完整的URL地址
   - **请求方法**: GET、POST等
   - **超时时间**: 毫秒为单位，建议5000-10000
   - **检查间隔**: 秒为单位，建议30-300
   - **请求头**: JSON格式，可选
   - **请求体**: JSON格式，POST请求时使用

### 查看监控数据

1. **监控概览**: 查看所有接口的实时状态和统计信息
2. **接口详情**: 点击接口名称查看详细监控数据
3. **手动检查**: 点击刷新按钮手动触发接口检查

### 示例接口测试

系统提供了一些测试接口用于验证监控功能：

- **成功接口**: `/api/test/success` - 总是返回成功
- **随机接口**: `/api/test/random` - 随机返回成功或失败
- **错误接口**: `/api/test/business-error` - 返回业务错误码
- **慢接口**: `/api/test/slow` - 模拟慢响应

## 故障排查

### 常见问题

1. **启动失败**
   - 检查Java环境：`java -version`
   - 检查端口占用：`netstat -tlnp | grep 8080`
   - 查看启动日志：`tail -f logs/startup.log`

2. **数据库连接失败**
   - 检查MySQL服务状态：`systemctl status mysql`
   - 验证数据库连接：`mysql -u username -p -h localhost`
   - 检查数据库配置和权限

3. **Redis连接失败**
   - 检查Redis服务状态：`systemctl status redis`
   - 验证Redis连接：`redis-cli ping`
   - 检查Redis配置和密码

4. **监控数据不更新**
   - 检查定时任务是否正常运行
   - 查看应用日志中的错误信息
   - 验证被监控的接口是否可访问

### 日志文件位置

- **应用日志**: `logs/api-monitor.log`
- **启动日志**: `logs/startup.log`
- **访问日志**: 通过Nginx配置

### 性能监控

- **JVM监控**: 通过Actuator端点查看内存使用情况
- **数据库监控**: 访问Druid监控页面
- **系统资源**: 使用`top`、`htop`等工具监控

## 维护操作

### 数据备份

```bash
# 备份数据库
mysqldump -u root -p api_monitor > api_monitor_backup_$(date +%Y%m%d).sql

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz src/main/resources/application.yml
```

### 数据清理

系统会自动清理历史数据，也可以手动执行：

```sql
-- 清理30天前的监控记录
CALL CleanupHistoryData(30, 90);
```

### 版本升级

1. 停止服务：`./start.sh stop`
2. 备份数据和配置
3. 替换新版本文件
4. 执行数据库升级脚本（如有）
5. 启动服务：`./start.sh start`

## 安全建议

1. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期更新数据库版本

2. **应用安全**
   - 配置防火墙规则
   - 使用HTTPS访问
   - 定期更新依赖包

3. **系统安全**
   - 创建专用用户运行应用
   - 设置合适的文件权限
   - 定期检查系统日志

---

如有问题，请查看日志文件或联系技术支持。
