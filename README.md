# API接口监控系统

基于Spring Boot开发的API接口监控系统，提供实时监控、统计分析、异常告警等功能。

## 功能特性

### 🎯 核心功能
- **实时监控**: 定时检查API接口可用性和响应时间
- **多维度监控**: 支持HTTP状态码、业务错误码、响应时间等多种监控指标
- **可视化面板**: 直观的监控概览和详情页面
- **统计分析**: 提供日、周、月等多维度统计数据
- **异常检测**: 自动识别接口异常并记录详细信息

### 📊 监控指标
- 接口名称和路径
- 实时状态（正常/异常/超时/网络错误）
- 今日调用量统计
- 平均响应时间
- 错误率计算
- 异常报错信息记录

### 🔧 技术特性
- 基于Spring Boot 2.7+
- 支持MySQL数据存储
- Redis缓存提升性能
- 响应式前端界面
- 异步任务处理
- 拦截器自动记录

## 快速开始

### 环境要求
- JDK 8+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd api-monitor
```

2. **数据库初始化**
```bash
# 连接MySQL数据库
mysql -u root -p

# 执行初始化脚本
source src/main/resources/sql/init.sql
```

3. **配置文件修改**
编辑 `src/main/resources/application.yml`，修改数据库和Redis连接信息：
```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

4. **编译运行**
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

5. **访问系统**
- 监控系统: http://localhost:8080/api-monitor/monitor
- 数据库监控: http://localhost:8080/api-monitor/druid
- 健康检查: http://localhost:8080/api-monitor/actuator/health

## 使用指南

### 添加监控接口

1. 访问配置管理页面
2. 点击"添加接口"按钮
3. 填写接口信息：
   - 接口名称：业务名称
   - 接口路径：完整的URL地址
   - 请求方法：GET/POST等
   - 超时时间：毫秒为单位
   - 检查间隔：秒为单位

### 查看监控数据

1. **监控概览页面**
   - 查看所有接口的实时状态
   - 统计正常、异常、超时接口数量
   - 支持搜索和筛选功能
   - 手动触发接口检查

2. **接口详情页面**
   - 查看单个接口的详细信息
   - 响应时间趋势图表
   - 最近监控记录列表
   - 今日统计数据

### 监控状态说明

| 状态 | 说明 | 判断条件 |
|------|------|----------|
| 正常 | 接口响应正常 | HTTP 200且无业务错误码 |
| 异常 | 接口返回错误 | HTTP 4xx/5xx或业务错误码 |
| 超时 | 接口响应超时 | 响应时间超过设定阈值 |
| 网络错误 | 网络连接异常 | 无法建立连接 |

### 业务错误码检测

系统会自动检测响应JSON中的错误码：
```json
{
  "code": -10014,
  "msg": "api request not allowed",
  "data": null
}
```

当`code`不为`0`、`200`或`success`时，会被识别为业务错误。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端页面      │    │   Spring Boot   │    │     MySQL       │
│   (监控面板)    │◄──►│   (核心服务)    │◄──►│   (数据存储)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │     Redis       │
                       │   (缓存层)      │
                       └─────────────────┘
```

### 核心组件

- **MonitorController**: 监控页面控制器
- **ApiMonitorService**: 核心监控服务
- **MonitorScheduledTask**: 定时监控任务
- **ApiMonitorInterceptor**: 请求拦截器
- **Repository层**: 数据访问层

## 配置说明

### 监控配置
```yaml
monitor:
  # 默认超时时间(毫秒)
  default-timeout: 5000
  # 默认检查间隔(秒)
  default-check-interval: 60
  # 线程池配置
  thread-pool:
    core-size: 5
    max-size: 10
    queue-capacity: 100
  # 缓存配置
  cache:
    statistics-ttl: 300
    config-ttl: 600
  # 数据清理配置
  cleanup:
    keep-record-days: 30
    keep-statistics-days: 90
```

### 定时任务

- **API检查任务**: 每分钟执行一次
- **统计更新任务**: 每5分钟执行一次
- **数据清理任务**: 每天凌晨2点执行

## 数据库设计

### 主要表结构

1. **api_config**: API配置表
2. **api_monitor_record**: 监控记录表
3. **api_statistics**: 统计数据表

详细的表结构请参考 `src/main/resources/sql/init.sql`

## 性能优化

### 缓存策略
- Redis缓存热点监控数据
- 本地缓存接口配置信息
- 异步处理监控记录

### 数据库优化
- 合理设计索引
- 定期清理历史数据
- 使用连接池管理连接

## 扩展功能

### 告警通知（待实现）
- 邮件告警
- 短信通知
- 钉钉/企业微信通知

### 数据导出（待实现）
- Excel报表导出
- 监控数据API
- 图表截图功能

## 常见问题

### Q: 如何添加自定义请求头？
A: 在API配置中的"请求头"字段填入JSON格式的头信息：
```json
{
  "Authorization": "Bearer token",
  "Content-Type": "application/json"
}
```

### Q: 如何监控需要认证的接口？
A: 在请求头中添加认证信息，或在请求体中包含认证参数。

### Q: 监控数据保存多长时间？
A: 默认保存监控记录30天，统计数据90天，可在配置文件中修改。

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 微信群讨论

---

**API监控系统** - 让API监控变得简单高效！
