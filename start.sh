#!/bin/bash

# API监控系统启动脚本
# 作者: monitor
# 日期: 2025-07-30

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="api-monitor"
JAR_NAME="api-monitor-1.0.0.jar"
MAIN_CLASS="com.monitor.ApiMonitorApplication"
JVM_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
PROFILES="prod"

# 目录配置
BASE_DIR=$(cd "$(dirname "$0")" && pwd)
LOG_DIR="$BASE_DIR/logs"
PID_FILE="$BASE_DIR/$PROJECT_NAME.pid"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 打印横幅
print_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "           API监控系统启动脚本"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查Java环境
check_java() {
    if [ -z "$JAVA_HOME" ]; then
        JAVA_CMD="java"
    else
        JAVA_CMD="$JAVA_HOME/bin/java"
    fi
    
    if ! command -v $JAVA_CMD &> /dev/null; then
        echo -e "${RED}错误: 未找到Java环境，请安装JDK 8或更高版本${NC}"
        exit 1
    fi
    
    JAVA_VERSION=$($JAVA_CMD -version 2>&1 | awk -F '"' '/version/ {print $2}')
    echo -e "${GREEN}Java版本: $JAVA_VERSION${NC}"
}

# 检查MySQL连接
check_mysql() {
    echo -e "${YELLOW}检查MySQL连接...${NC}"
    # 这里可以添加MySQL连接检查逻辑
    echo -e "${GREEN}MySQL连接检查完成${NC}"
}

# 检查Redis连接
check_redis() {
    echo -e "${YELLOW}检查Redis连接...${NC}"
    # 这里可以添加Redis连接检查逻辑
    echo -e "${GREEN}Redis连接检查完成${NC}"
}

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start() {
    print_banner
    
    if is_running; then
        echo -e "${YELLOW}应用已经在运行中 (PID: $(cat $PID_FILE))${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}正在启动 $PROJECT_NAME...${NC}"
    
    # 环境检查
    check_java
    check_mysql
    check_redis
    
    # 编译项目（如果需要）
    if [ ! -f "target/$JAR_NAME" ]; then
        echo -e "${YELLOW}正在编译项目...${NC}"
        mvn clean package -DskipTests
        if [ $? -ne 0 ]; then
            echo -e "${RED}编译失败${NC}"
            exit 1
        fi
    fi
    
    # 启动应用
    nohup $JAVA_CMD $JVM_OPTS \
        -Dspring.profiles.active=$PROFILES \
        -Dfile.encoding=UTF-8 \
        -Djava.awt.headless=true \
        -jar target/$JAR_NAME \
        > "$LOG_DIR/startup.log" 2>&1 &
    
    PID=$!
    echo $PID > "$PID_FILE"
    
    # 等待启动
    echo -e "${YELLOW}等待应用启动...${NC}"
    sleep 5
    
    if is_running; then
        echo -e "${GREEN}✓ $PROJECT_NAME 启动成功 (PID: $PID)${NC}"
        echo -e "${GREEN}✓ 访问地址: http://localhost:8080/api-monitor${NC}"
        echo -e "${GREEN}✓ 监控面板: http://localhost:8080/api-monitor/monitor${NC}"
        echo -e "${GREEN}✓ 数据库监控: http://localhost:8080/api-monitor/druid${NC}"
        echo -e "${GREEN}✓ 日志文件: $LOG_DIR/api-monitor.log${NC}"
    else
        echo -e "${RED}✗ 启动失败，请检查日志: $LOG_DIR/startup.log${NC}"
        exit 1
    fi
}

# 停止应用
stop() {
    if ! is_running; then
        echo -e "${YELLOW}应用未运行${NC}"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    echo -e "${YELLOW}正在停止 $PROJECT_NAME (PID: $PID)...${NC}"
    
    kill $PID
    
    # 等待进程结束
    for i in {1..30}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            break
        fi
        sleep 1
    done
    
    if ps -p $PID > /dev/null 2>&1; then
        echo -e "${YELLOW}强制终止进程...${NC}"
        kill -9 $PID
    fi
    
    rm -f "$PID_FILE"
    echo -e "${GREEN}✓ $PROJECT_NAME 已停止${NC}"
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

# 查看状态
status() {
    if is_running; then
        PID=$(cat "$PID_FILE")
        echo -e "${GREEN}✓ $PROJECT_NAME 正在运行 (PID: $PID)${NC}"
        
        # 显示内存使用情况
        MEMORY=$(ps -p $PID -o rss= | awk '{print int($1/1024)"MB"}')
        echo -e "${BLUE}内存使用: $MEMORY${NC}"
        
        # 显示运行时间
        UPTIME=$(ps -p $PID -o etime= | tr -d ' ')
        echo -e "${BLUE}运行时间: $UPTIME${NC}"
    else
        echo -e "${RED}✗ $PROJECT_NAME 未运行${NC}"
    fi
}

# 查看日志
logs() {
    if [ -f "$LOG_DIR/api-monitor.log" ]; then
        tail -f "$LOG_DIR/api-monitor.log"
    else
        echo -e "${YELLOW}日志文件不存在${NC}"
    fi
}

# 健康检查
health() {
    if ! is_running; then
        echo -e "${RED}✗ 应用未运行${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}正在进行健康检查...${NC}"
    
    # 检查HTTP端口
    if curl -s http://localhost:8080/api-monitor/actuator/health > /dev/null; then
        echo -e "${GREEN}✓ HTTP服务正常${NC}"
    else
        echo -e "${RED}✗ HTTP服务异常${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✓ 健康检查通过${NC}"
}

# 显示帮助信息
usage() {
    echo "用法: $0 {start|stop|restart|status|logs|health}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动应用"
    echo "  stop    - 停止应用"
    echo "  restart - 重启应用"
    echo "  status  - 查看运行状态"
    echo "  logs    - 查看实时日志"
    echo "  health  - 健康检查"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动应用"
    echo "  $0 status   # 查看状态"
    echo "  $0 logs     # 查看日志"
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    health)
        health
        ;;
    *)
        usage
        exit 1
        ;;
esac

exit 0
