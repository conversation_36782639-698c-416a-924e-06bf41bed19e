2025-07-30 13:28:16 [main] INFO  com.monitor.ApiMonitorApplication - Starting ApiMonitorApplication v1.0.0 using Java 1.8.0_351 on MacBook-Pro.local with PID 69266 (/Users/<USER>/Desktop/cq/CQ/素材库/logjiankong/target/api-monitor-1.0.0.jar started by apple in /Users/<USER>/Desktop/cq/CQ/素材库/logjiankong)
2025-07-30 13:28:16 [main] DEBUG com.monitor.ApiMonitorApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-30 13:28:16 [main] INFO  com.monitor.ApiMonitorApplication - The following 1 profile is active: "prod"
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 307 ms. Found 3 JPA repository interfaces.
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiMonitorRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:28:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-07-30 13:28:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-30 13:28:21 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 13:28:21 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-30 13:28:21 [main] INFO  o.a.c.c.C.[.[.[/api-monitor] - Initializing Spring embedded WebApplicationContext
2025-07-30 13:28:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4953 ms
2025-07-30 13:28:21 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-30 13:28:22 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-30 13:28:23 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 13:28:23 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-30 13:28:23 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 13:28:24 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-30 13:28:24 [main] ERROR o.s.o.j.LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: Unable to create unique key constraint (apiId, statDate) on table api_statistics: database column 'statDate', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
2025-07-30 13:28:24 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.AnnotationException: Unable to create unique key constraint (apiId, statDate) on table api_statistics: database column 'statDate', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
2025-07-30 13:28:24 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-30 13:28:24 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-30 13:28:24 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 13:28:24 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 13:28:24 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.AnnotationException: Unable to create unique key constraint (apiId, statDate) on table api_statistics: database column 'statDate', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.monitor.ApiMonitorApplication.main(ApiMonitorApplication.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: org.hibernate.AnnotationException: Unable to create unique key constraint (apiId, statDate) on table api_statistics: database column 'statDate', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.buildUniqueKeyFromColumnNames(InFlightMetadataCollectorImpl.java:2060)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.buildUniqueKeyFromColumnNames(InFlightMetadataCollectorImpl.java:1921)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.processUniqueConstraintHolders(InFlightMetadataCollectorImpl.java:1909)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.processSecondPasses(InFlightMetadataCollectorImpl.java:1635)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:295)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 24 common frames omitted
2025-07-30 13:29:40 [main] INFO  com.monitor.ApiMonitorApplication - Starting ApiMonitorApplication v1.0.0 using Java 1.8.0_351 on MacBook-Pro.local with PID 71107 (/Users/<USER>/Desktop/cq/CQ/素材库/logjiankong/target/api-monitor-1.0.0.jar started by apple in /Users/<USER>/Desktop/cq/CQ/素材库/logjiankong)
2025-07-30 13:29:40 [main] DEBUG com.monitor.ApiMonitorApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-30 13:29:40 [main] INFO  com.monitor.ApiMonitorApplication - The following 1 profile is active: "prod"
2025-07-30 13:29:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:29:42 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 312 ms. Found 3 JPA repository interfaces.
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiMonitorRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:29:43 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-07-30 13:29:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-30 13:29:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 13:29:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-30 13:29:45 [main] INFO  o.a.c.c.C.[.[.[/api-monitor] - Initializing Spring embedded WebApplicationContext
2025-07-30 13:29:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4817 ms
2025-07-30 13:29:45 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-30 13:29:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-30 13:29:47 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 13:29:47 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-30 13:29:47 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 13:29:48 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-30 13:29:48 [main] ERROR o.s.o.j.LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
2025-07-30 13:29:48 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.AnnotationException: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
2025-07-30 13:29:48 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-30 13:29:48 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-30 13:29:48 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 13:29:48 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 13:29:48 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.AnnotationException: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.monitor.ApiMonitorApplication.main(ApiMonitorApplication.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: org.hibernate.AnnotationException: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.buildUniqueKeyFromColumnNames(InFlightMetadataCollectorImpl.java:2060)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.processJPAIndexHolders(InFlightMetadataCollectorImpl.java:2073)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.processSecondPasses(InFlightMetadataCollectorImpl.java:1636)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:295)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 24 common frames omitted
2025-07-30 13:30:15 [main] INFO  com.monitor.ApiMonitorApplication - Starting ApiMonitorApplication v1.0.0 using Java 1.8.0_351 on MacBook-Pro.local with PID 71948 (/Users/<USER>/Desktop/cq/CQ/素材库/logjiankong/target/api-monitor-1.0.0.jar started by apple in /Users/<USER>/Desktop/cq/CQ/素材库/logjiankong)
2025-07-30 13:30:15 [main] DEBUG com.monitor.ApiMonitorApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-30 13:30:15 [main] INFO  com.monitor.ApiMonitorApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 325 ms. Found 3 JPA repository interfaces.
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiMonitorRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:30:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-07-30 13:30:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-30 13:30:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 13:30:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-30 13:30:20 [main] INFO  o.a.c.c.C.[.[.[/api-monitor] - Initializing Spring embedded WebApplicationContext
2025-07-30 13:30:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4949 ms
2025-07-30 13:30:20 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-30 13:30:21 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-30 13:30:22 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 13:30:22 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-30 13:30:22 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 13:30:22 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-30 13:30:23 [main] ERROR o.s.o.j.LocalContainerEntityManagerFactoryBean - Failed to initialize JPA EntityManagerFactory: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
2025-07-30 13:30:23 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.AnnotationException: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
2025-07-30 13:30:23 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-30 13:30:23 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-30 13:30:23 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-30 13:30:23 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 13:30:23 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is org.hibernate.AnnotationException: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1157)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:911)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.monitor.ApiMonitorApplication.main(ApiMonitorApplication.java:20)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: org.hibernate.AnnotationException: Unable to create index (apiId, checkTime) on table api_monitor_record: database column 'checkTime', 'apiId' not found. Make sure that you use the correct column name which depends on the naming strategy in use (it may not be the same as the property name in the entity, especially for relational types)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.buildUniqueKeyFromColumnNames(InFlightMetadataCollectorImpl.java:2060)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.processJPAIndexHolders(InFlightMetadataCollectorImpl.java:2073)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.processSecondPasses(InFlightMetadataCollectorImpl.java:1636)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:295)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1460)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1494)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:58)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:365)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 24 common frames omitted
2025-07-30 13:31:32 [main] INFO  com.monitor.ApiMonitorApplication - Starting ApiMonitorApplication v1.0.0 using Java 1.8.0_351 on MacBook-Pro.local with PID 73546 (/Users/<USER>/Desktop/cq/CQ/素材库/logjiankong/target/api-monitor-1.0.0.jar started by apple in /Users/<USER>/Desktop/cq/CQ/素材库/logjiankong)
2025-07-30 13:31:32 [main] DEBUG com.monitor.ApiMonitorApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-30 13:31:32 [main] INFO  com.monitor.ApiMonitorApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-30 13:31:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:31:34 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 309 ms. Found 3 JPA repository interfaces.
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiConfigRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.monitor.repository.ApiMonitorRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-07-30 13:31:35 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-30 13:31:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-30 13:31:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 13:31:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-30 13:31:37 [main] INFO  o.a.c.c.C.[.[.[/api-monitor] - Initializing Spring embedded WebApplicationContext
2025-07-30 13:31:37 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4630 ms
2025-07-30 13:31:37 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-30 13:31:38 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-30 13:31:38 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 13:31:38 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.15.Final
2025-07-30 13:31:39 [main] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 13:31:39 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-07-30 13:31:40 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-30 13:31:40 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 13:31:42 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 13:31:43 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-30 13:31:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api-monitor'
2025-07-30 13:31:43 [scheduling-1] DEBUG c.monitor.task.MonitorScheduledTask - 开始执行定时API检查任务
2025-07-30 13:31:43 [main] INFO  com.monitor.ApiMonitorApplication - Started ApiMonitorApplication in 12.207 seconds (JVM running for 12.996)
2025-07-30 13:31:43 [scheduling-1] DEBUG c.monitor.task.MonitorScheduledTask - 开始更新统计数据
2025-07-30 13:31:43 [scheduling-1] INFO  c.m.s.impl.ApiMonitorServiceImpl - 更新统计数据
2025-07-30 13:31:43 [task-1] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_ 
    where
        apiconfig0_.is_enabled=1
2025-07-30 13:31:44 [task-1] INFO  c.m.s.impl.ApiMonitorServiceImpl - 开始检查 14 个启用的API
2025-07-30 13:31:44 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:44.338]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [242]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:44 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=1, Status=ERROR, ResponseTime=242ms
2025-07-30 13:31:44 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:44.558]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [10]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:44 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=2, Status=ERROR, ResponseTime=10ms
2025-07-30 13:31:44 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:44.575]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [6]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:44 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=3, Status=ERROR, ResponseTime=6ms
2025-07-30 13:31:44 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:44.590]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [7]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:44 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=4, Status=ERROR, ResponseTime=7ms
2025-07-30 13:31:44 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:44.604]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [4]
2025-07-30 13:31:44 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:44 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=5, Status=ERROR, ResponseTime=4ms
2025-07-30 13:31:45 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:45.394]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [723]
2025-07-30 13:31:45 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:31:45 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=6, Status=SUCCESS, ResponseTime=723ms
2025-07-30 13:31:46 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:46 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:46.998]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1580]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=7, Status=SUCCESS, ResponseTime=1580ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.012]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [5]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=8, Status=ERROR, ResponseTime=5ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.025]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=9, Status=ERROR, ResponseTime=2ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.036]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=10, Status=ERROR, ResponseTime=2ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.045]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=11, Status=ERROR, ResponseTime=2ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.052]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=12, Status=ERROR, ResponseTime=2ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.239]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [173]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=13, Status=SUCCESS, ResponseTime=173ms
2025-07-30 13:31:47 [task-1] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:31:47.672]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [422]
2025-07-30 13:31:47 [task-1] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:31:47 [task-1] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=14, Status=SUCCESS, ResponseTime=422ms
2025-07-30 13:31:48 [task-1] INFO  c.m.s.impl.ApiMonitorServiceImpl - 完成API检查任务
2025-07-30 13:32:34 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/api-monitor] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 13:32:34 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 13:32:34 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 19 ms
2025-07-30 13:32:42 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:43 [http-nio-8080-exec-2] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:43 [http-nio-8080-exec-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:43 [scheduling-1] DEBUG c.monitor.task.MonitorScheduledTask - 开始执行定时API检查任务
2025-07-30 13:32:43 [task-2] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_ 
    where
        apiconfig0_.is_enabled=1
2025-07-30 13:32:43 [task-2] INFO  c.m.s.impl.ApiMonitorServiceImpl - 开始检查 14 个启用的API
2025-07-30 13:32:43 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:43.874]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [26]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:43 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=1, Status=ERROR, ResponseTime=26ms
2025-07-30 13:32:43 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:43.915]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [9]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:43 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=2, Status=ERROR, ResponseTime=9ms
2025-07-30 13:32:43 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:43.968]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [17]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:43 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=3, Status=ERROR, ResponseTime=17ms
2025-07-30 13:32:43 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:43.986]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [4]
2025-07-30 13:32:43 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:43 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=4, Status=ERROR, ResponseTime=4ms
2025-07-30 13:32:44 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:44]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [3]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:44 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=5, Status=ERROR, ResponseTime=3ms
2025-07-30 13:32:44 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:44.120]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [105]
2025-07-30 13:32:44 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:32:44 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=6, Status=SUCCESS, ResponseTime=105ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.589]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1457]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=7, Status=SUCCESS, ResponseTime=1457ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.609]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [3]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=8, Status=ERROR, ResponseTime=3ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.622]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [4]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=9, Status=ERROR, ResponseTime=4ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.632]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [5]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=10, Status=ERROR, ResponseTime=5ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.639]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=11, Status=ERROR, ResponseTime=1ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.643]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=12, Status=ERROR, ResponseTime=2ms
2025-07-30 13:32:45 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:45.774]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [116]
2025-07-30 13:32:45 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:32:45 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=13, Status=SUCCESS, ResponseTime=116ms
2025-07-30 13:32:46 [task-2] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:32:46.155]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [368]
2025-07-30 13:32:46 [task-2] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:32:46 [task-2] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=14, Status=SUCCESS, ResponseTime=368ms
2025-07-30 13:32:46 [task-2] INFO  c.m.s.impl.ApiMonitorServiceImpl - 完成API检查任务
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        apimonitor0_.id as id1_1_,
        apimonitor0_.api_id as api_id2_1_,
        apimonitor0_.check_time as check_ti3_1_,
        apimonitor0_.check_type as check_ty4_1_,
        apimonitor0_.error_code as error_co5_1_,
        apimonitor0_.error_message as error_me6_1_,
        apimonitor0_.http_status as http_sta7_1_,
        apimonitor0_.response_content as response8_1_,
        apimonitor0_.response_time as response9_1_,
        apimonitor0_.status as status10_1_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
    order by
        apimonitor0_.check_time DESC limit ?
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        avg(apimonitor0_.response_time) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and date(apimonitor0_.check_time)=CURRENT_DATE 
        and (
            apimonitor0_.response_time is not null
        )
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:32:54 [http-nio-8080-exec-7] DEBUG org.hibernate.SQL - 
    select
        count(apimonitor0_.id) as col_0_0_ 
    from
        api_monitor_record apimonitor0_ 
    where
        apimonitor0_.api_id=? 
        and apimonitor0_.status='SUCCESS' 
        and date(apimonitor0_.check_time)=CURRENT_DATE
2025-07-30 13:32:54 [http-nio-8080-exec-7] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:33:30 [http-nio-8080-exec-7] ERROR c.m.controller.MonitorController - 获取监控概览失败
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:75)
	at org.springframework.data.redis.core.AbstractOperations.deserializeValue(AbstractOperations.java:360)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:62)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.monitor.service.impl.ApiMonitorServiceImpl.getAllApiMonitorOverview(ApiMonitorServiceImpl.java:72)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$FastClassBySpringCGLIB$$7250a981.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$EnhancerBySpringCGLIB$$51e542c2.getAllApiMonitorOverview(<generated>)
	at com.monitor.controller.MonitorController.index(MonitorController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1127)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2036)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1700)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1678)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:357)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3731)
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:73)
	... 71 common frames omitted
2025-07-30 13:33:34 [http-nio-8080-exec-8] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_
2025-07-30 13:33:35 [http-nio-8080-exec-2] ERROR c.m.controller.MonitorController - 获取监控概览失败
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:75)
	at org.springframework.data.redis.core.AbstractOperations.deserializeValue(AbstractOperations.java:360)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:62)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.monitor.service.impl.ApiMonitorServiceImpl.getAllApiMonitorOverview(ApiMonitorServiceImpl.java:72)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$FastClassBySpringCGLIB$$7250a981.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$EnhancerBySpringCGLIB$$51e542c2.getAllApiMonitorOverview(<generated>)
	at com.monitor.controller.MonitorController.index(MonitorController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1127)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2036)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1700)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1678)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:357)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3731)
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:73)
	... 71 common frames omitted
2025-07-30 13:33:36 [http-nio-8080-exec-9] ERROR c.m.controller.MonitorController - 获取监控概览失败
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:75)
	at org.springframework.data.redis.core.AbstractOperations.deserializeValue(AbstractOperations.java:360)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:62)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.monitor.service.impl.ApiMonitorServiceImpl.getAllApiMonitorOverview(ApiMonitorServiceImpl.java:72)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$FastClassBySpringCGLIB$$7250a981.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$EnhancerBySpringCGLIB$$51e542c2.getAllApiMonitorOverview(<generated>)
	at com.monitor.controller.MonitorController.index(MonitorController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1127)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2036)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1700)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1678)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:357)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3731)
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:73)
	... 71 common frames omitted
2025-07-30 13:33:37 [http-nio-8080-exec-1] ERROR c.m.controller.MonitorController - 获取监控概览失败
org.springframework.data.redis.serializer.SerializationException: Could not read JSON: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"]); nested exception is com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:75)
	at org.springframework.data.redis.core.AbstractOperations.deserializeValue(AbstractOperations.java:360)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:62)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:224)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.monitor.service.impl.ApiMonitorServiceImpl.getAllApiMonitorOverview(ApiMonitorServiceImpl.java:72)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$FastClassBySpringCGLIB$$7250a981.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.monitor.service.impl.ApiMonitorServiceImpl$$EnhancerBySpringCGLIB$$51e542c2.getAllApiMonitorOverview(<generated>)
	at com.monitor.controller.MonitorController.index(MonitorController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "statusClass" (class com.monitor.dto.ApiMonitorDTO), not marked as ignorable (12 known properties: "avgResponseTime", "status", "statusDesc", "apiId", "isEnabled", "errorRate", "todayCount", "apiPath", "consecutiveFailures", "apiName", "lastCheckTime", "lastErrorMessage"])
 at [Source: (byte[])"["java.util.ArrayList",[["com.monitor.dto.ApiMonitorDTO",{"apiId":1,"apiName":"用户登录接口","apiPath":"http://localhost:8080/api/user/login","status":"ERROR","statusDesc":null,"todayCount":8,"avgResponseTime":["java.math.BigDecimal",1347.25],"errorRate":["java.math.BigDecimal",50.00],"lastCheckTime":[2025,7,30,13,32,44],"lastErrorMessage":"HTTP错误: 404","isEnabled":true,"consecutiveFailures":null,"statusClass":"danger","statusIcon":"fas fa-times-circle"}],["com.monitor.dto.ApiMonitorDT"[truncated 5619 bytes]; line: 1, column: 426] (through reference chain: java.util.ArrayList[0]->com.monitor.dto.ApiMonitorDTO["statusClass"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61)
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1127)
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2036)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1700)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1678)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:320)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer._deserializeFromArray(CollectionDeserializer.java:357)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:244)
	at com.fasterxml.jackson.databind.deser.std.CollectionDeserializer.deserialize(CollectionDeserializer.java:28)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer._deserialize(AsArrayTypeDeserializer.java:120)
	at com.fasterxml.jackson.databind.jsontype.impl.AsArrayTypeDeserializer.deserializeTypedFromAny(AsArrayTypeDeserializer.java:71)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializer$Vanilla.deserializeWithType(UntypedObjectDeserializer.java:781)
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:323)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4674)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3731)
	at org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer.deserialize(Jackson2JsonRedisSerializer.java:73)
	... 71 common frames omitted
2025-07-30 13:33:43 [scheduling-1] DEBUG c.monitor.task.MonitorScheduledTask - 开始执行定时API检查任务
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_ 
    where
        apiconfig0_.is_enabled=1
2025-07-30 13:33:43 [task-3] INFO  c.m.s.impl.ApiMonitorServiceImpl - 开始检查 14 个启用的API
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:43.747]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [9]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:43 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=1, Status=ERROR, ResponseTime=9ms
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:43.763]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:43 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=2, Status=ERROR, ResponseTime=1ms
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:43.772]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:43 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=3, Status=ERROR, ResponseTime=2ms
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:43.779]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:43 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=4, Status=ERROR, ResponseTime=2ms
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:43.788]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:43 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=5, Status=ERROR, ResponseTime=1ms
2025-07-30 13:33:43 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:43.969]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [170]
2025-07-30 13:33:43 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:33:43 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=6, Status=SUCCESS, ResponseTime=170ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.492]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1510]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=7, Status=SUCCESS, ResponseTime=1510ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.502]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [4]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=8, Status=ERROR, ResponseTime=4ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.508]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=9, Status=ERROR, ResponseTime=1ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.517]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=10, Status=ERROR, ResponseTime=2ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.529]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [4]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=11, Status=ERROR, ResponseTime=4ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.543]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [5]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=12, Status=ERROR, ResponseTime=5ms
2025-07-30 13:33:45 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:45.663]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [107]
2025-07-30 13:33:45 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:33:45 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=13, Status=SUCCESS, ResponseTime=107ms
2025-07-30 13:33:46 [task-3] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:33:46.046]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [372]
2025-07-30 13:33:46 [task-3] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:33:46 [task-3] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=14, Status=SUCCESS, ResponseTime=372ms
2025-07-30 13:33:46 [task-3] INFO  c.m.s.impl.ApiMonitorServiceImpl - 完成API检查任务
2025-07-30 13:34:43 [scheduling-1] DEBUG c.monitor.task.MonitorScheduledTask - 开始执行定时API检查任务
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_ 
    where
        apiconfig0_.is_enabled=1
2025-07-30 13:34:43 [task-4] INFO  c.m.s.impl.ApiMonitorServiceImpl - 开始检查 14 个启用的API
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [1]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:43.777]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [17]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:43 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=1, Status=ERROR, ResponseTime=17ms
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [2]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:43.788]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:43 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=2, Status=ERROR, ResponseTime=1ms
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [3]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:43.797]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [3]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:43 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=3, Status=ERROR, ResponseTime=3ms
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [4]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:43.803]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:43 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=4, Status=ERROR, ResponseTime=2ms
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [5]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:43.808]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [2]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:43 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=5, Status=ERROR, ResponseTime=2ms
2025-07-30 13:34:43 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [6]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:43.943]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [127]
2025-07-30 13:34:43 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:34:43 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=6, Status=SUCCESS, ResponseTime=127ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [7]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.416]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1463]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=7, Status=SUCCESS, ResponseTime=1463ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [8]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.438]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [12]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=8, Status=ERROR, ResponseTime=12ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [9]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.447]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [6]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=9, Status=ERROR, ResponseTime=6ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [10]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.456]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [4]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=10, Status=ERROR, ResponseTime=4ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [11]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.471]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=11, Status=ERROR, ResponseTime=1ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [12]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.476]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [HTTP错误: 404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [404]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!doctype html><html lang="en"><head><title>HTTP Status 404 – Not Found</title><style type="text/css">body {font-family:Tahoma,Arial,sans-serif;} h1, h2, h3, b {color:white;background-color:#525D76;} h1 {font-size:22px;} h2 {font-size:16px;} h3 {font-size:14px;} p {font-size:12px;} a {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 404 – Not Found</h1></body></html>]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [1]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [ERROR]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=12, Status=ERROR, ResponseTime=1ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [13]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.582]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [<!DOCTYPE html><!--STATUS OK--><html><head><meta http-equiv="Content-Type" content="text/html;charset=utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta content="origin-when-cross-origin" name="referrer"><meta name="theme-color" content="#ffffff"><meta name="description" content="全球领先的中文搜索引擎、致力于让网民更便捷地获取信息，找到所求。百度超过千亿的中文网页数据库，可以瞬间找到相关的搜索结果。"><link rel="shortcut icon" href="https://www.baidu.com/favicon.ico" type="image/x-icon" /><link rel="search" type="application/opensearchdescription+xml" href="/content-search.xml" title="百度搜索" /><link rel="stylesheet" data-for="result" href="https://pss.bdstatic.com/r/www/static/font/cosmic/pc/cos-icon_3ff597f.css"/><link rel="icon" sizes="any" mask href="https://www.baidu.com/favicon.ico"><link rel="dns-prefetch" href="//dss0.bdstatic.com"/><link rel="dns-prefetch" href="//dss1.bdstatic.com"/><link rel="dns-prefetch" href="//ss1.bdstatic.com"/><link rel="dns-prefetch" href="//sp0.baidu.com"/><link rel="dns-prefetch" href="/]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [98]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=13, Status=SUCCESS, ResponseTime=98ms
2025-07-30 13:34:45 [task-4] DEBUG org.hibernate.SQL - 
    insert 
    into
        api_monitor_record
        (api_id, check_time, check_type, error_code, error_message, http_status, response_content, response_time, status) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [1] as [BIGINT] - [14]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [2] as [TIMESTAMP] - [2025-07-30T13:34:45.964]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [3] as [VARCHAR] - [SCHEDULED]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [4] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [5] as [VARCHAR] - [null]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [6] as [INTEGER] - [200]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [7] as [VARCHAR] - [{
  "current_user_url": "https://api.github.com/user",
  "current_user_authorizations_html_url": "https://github.com/settings/connections/applications{/client_id}",
  "authorizations_url": "https://api.github.com/authorizations",
  "code_search_url": "https://api.github.com/search/code?q={query}{&page,per_page,sort,order}",
  "commit_search_url": "https://api.github.com/search/commits?q={query}{&page,per_page,sort,order}",
  "emails_url": "https://api.github.com/user/emails",
  "emojis_url": "https://api.github.com/emojis",
  "events_url": "https://api.github.com/events",
  "feeds_url": "https://api.github.com/feeds",
  "followers_url": "https://api.github.com/user/followers",
  "following_url": "https://api.github.com/user/following{/target}",
  "gists_url": "https://api.github.com/gists{/gist_id}",
  "hub_url": "https://api.github.com/hub",
  "issue_search_url": "https://api.github.com/search/issues?q={query}{&page,per_page,sort,order}",
  "issues_url": "https://api.github.com/issues]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [8] as [INTEGER] - [370]
2025-07-30 13:34:45 [task-4] TRACE o.h.type.descriptor.sql.BasicBinder - binding parameter [9] as [VARCHAR] - [SUCCESS]
2025-07-30 13:34:45 [task-4] DEBUG c.m.s.impl.ApiMonitorServiceImpl - 记录监控结果: API=14, Status=SUCCESS, ResponseTime=370ms
2025-07-30 13:34:45 [task-4] INFO  c.m.s.impl.ApiMonitorServiceImpl - 完成API检查任务
2025-07-30 13:35:16 [http-nio-8080-exec-5] DEBUG org.hibernate.SQL - 
    select
        apiconfig0_.id as id1_0_,
        apiconfig0_.api_method as api_meth2_0_,
        apiconfig0_.api_name as api_name3_0_,
        apiconfig0_.api_path as api_path4_0_,
        apiconfig0_.check_interval as check_in5_0_,
        apiconfig0_.created_time as created_6_0_,
        apiconfig0_.expected_response as expected7_0_,
        apiconfig0_.headers as headers8_0_,
        apiconfig0_.is_enabled as is_enabl9_0_,
        apiconfig0_.request_body as request10_0_,
        apiconfig0_.timeout_ms as timeout11_0_,
        apiconfig0_.updated_time as updated12_0_ 
    from
        api_config apiconfig0_
2025-07-30 13:35:17 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 13:35:17 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-30 13:35:17 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
